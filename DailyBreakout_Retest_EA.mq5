//+------------------------------------------------------------------+
//|                                      DailyBreakout_Retest_EA.mq5 |
//|                                  Copyright 2024, Augment Agent    |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Augment Agent"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Daily High/Low Breakout and Retest Strategy"
#property description "Marks daily high/low, trades breakouts with retest confirmation"

//--- Include necessary libraries
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>

//--- Global objects
CTrade         trade;
CPositionInfo  position;

//+------------------------------------------------------------------+
//| Input Parameters                                                 |
//+------------------------------------------------------------------+
input group "=== STRATEGY SETTINGS ==="
input bool     InpEnableTrading = true;                    // Enable Trading
input int      InpMagicNumber = 789123;                    // Magic Number
input string   InpTradeComment = "DailyBreakout_EA";       // Trade Comment

input group "=== DAILY LEVELS ==="
input int      InpDayStartHour = 0;                        // Day Start Hour (0 = Midnight)
input int      InpLookbackBars = 96;                       // Lookback Bars for High/Low (96 = 24h on M15)
input double   InpBreakoutPips = 5.0;                      // Breakout Confirmation (Pips)
input double   InpRetestPips = 10.0;                       // Retest Distance (Pips)

input group "=== MONEY MANAGEMENT ==="
input double   InpRiskPercent = 1.0;                       // Risk Per Trade (%)
input double   InpStopLossPips = 50.0;                     // Stop Loss (Pips)
input double   InpTakeProfitPips = 100.0;                  // Take Profit (Pips)
input double   InpMaxDailyLoss = 3.0;                      // Max Daily Loss (%)
input int      InpMaxTradesPerDay = 5;                     // Max Trades Per Day

input group "=== TRADE MANAGEMENT ==="
input bool     InpUseTrailingStop = true;                  // Use Trailing Stop
input double   InpTrailingStopPips = 30.0;                 // Trailing Stop Distance (Pips)
input double   InpTrailingStepPips = 10.0;                 // Trailing Step (Pips)

input group "=== LOGGING ==="
input bool     InpEnableLogging = true;                    // Enable Logging
input bool     InpShowLevels = true;                       // Show Levels on Chart

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
// Daily levels
double daily_high;
double daily_low;
datetime daily_start_time;
bool levels_set;

// Breakout tracking
bool high_broken;
bool low_broken;
bool waiting_for_retest_high;
bool waiting_for_retest_low;
datetime breakout_time_high;
datetime breakout_time_low;

// Money management
double daily_start_balance;
int trades_today;
datetime last_trade_date;

// Price levels
double pip_size;
double breakout_distance;
double retest_distance;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Set magic number
    trade.SetExpertMagicNumber(InpMagicNumber);
    
    // Calculate pip size
    pip_size = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    if(SymbolInfoInteger(_Symbol, SYMBOL_DIGITS) == 5 || SymbolInfoInteger(_Symbol, SYMBOL_DIGITS) == 3)
        pip_size *= 10;
    
    breakout_distance = InpBreakoutPips * pip_size;
    retest_distance = InpRetestPips * pip_size;
    
    // Initialize variables
    daily_high = 0;
    daily_low = 0;
    daily_start_time = 0;
    levels_set = false;
    
    high_broken = false;
    low_broken = false;
    waiting_for_retest_high = false;
    waiting_for_retest_low = false;
    breakout_time_high = 0;
    breakout_time_low = 0;
    
    daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    trades_today = 0;
    last_trade_date = 0;
    
    if(InpEnableLogging)
        Print("Daily Breakout Retest EA initialized on ", _Symbol);
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Clean up chart objects
    if(InpShowLevels)
    {
        ObjectDelete(0, "DailyHigh");
        ObjectDelete(0, "DailyLow");
    }
    
    if(InpEnableLogging)
        Print("Daily Breakout Retest EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check if new bar
    static datetime last_bar_time = 0;
    datetime current_bar_time = iTime(_Symbol, _Period, 0);
    
    if(current_bar_time == last_bar_time)
        return;
    
    last_bar_time = current_bar_time;
    
    // Update daily tracking
    UpdateDailyTracking();
    
    // Check money management
    if(!CheckMoneyManagement())
        return;
    
    // Set daily levels if needed
    SetDailyLevels();
    
    // Check for breakouts
    CheckBreakouts();
    
    // Check for retests and entries
    CheckRetestEntries();
    
    // Manage existing positions
    ManagePositions();
}

//+------------------------------------------------------------------+
//| Update Daily Tracking                                           |
//+------------------------------------------------------------------+
void UpdateDailyTracking()
{
    datetime current_date = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
    
    if(last_trade_date != current_date)
    {
        // New day - reset counters and levels
        trades_today = 0;
        daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
        last_trade_date = current_date;
        
        // Reset daily levels for new day
        levels_set = false;
        high_broken = false;
        low_broken = false;
        waiting_for_retest_high = false;
        waiting_for_retest_low = false;
        
        if(InpEnableLogging)
            Print("New trading day started. Balance: ", daily_start_balance);
    }
}

//+------------------------------------------------------------------+
//| Check Money Management                                           |
//+------------------------------------------------------------------+
bool CheckMoneyManagement()
{
    double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    
    // Check daily loss limit
    double daily_loss = (daily_start_balance - current_balance) / daily_start_balance * 100;
    if(daily_loss >= InpMaxDailyLoss)
    {
        if(InpEnableLogging)
            Print("Daily loss limit reached: ", daily_loss, "%");
        return false;
    }
    
    // Check max trades per day
    if(trades_today >= InpMaxTradesPerDay)
    {
        if(InpEnableLogging)
            Print("Maximum trades per day reached: ", trades_today);
        return false;
    }
    
    return InpEnableTrading;
}

//+------------------------------------------------------------------+
//| Set Daily High/Low Levels                                       |
//+------------------------------------------------------------------+
void SetDailyLevels()
{
    if(levels_set)
        return;
    
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    
    // Check if we're at the start of a new day
    if(dt.hour >= InpDayStartHour || daily_start_time == 0)
    {
        double high_prices[], low_prices[];
        ArraySetAsSeries(high_prices, true);
        ArraySetAsSeries(low_prices, true);
        
        // Get historical data for lookback period
        if(CopyHigh(_Symbol, _Period, 1, InpLookbackBars, high_prices) == InpLookbackBars &&
           CopyLow(_Symbol, _Period, 1, InpLookbackBars, low_prices) == InpLookbackBars)
        {
            // Find highest high and lowest low
            daily_high = high_prices[ArrayMaximum(high_prices)];
            daily_low = low_prices[ArrayMinimum(low_prices)];
            
            daily_start_time = TimeCurrent();
            levels_set = true;
            
            // Draw levels on chart
            if(InpShowLevels)
                DrawLevels();
            
            if(InpEnableLogging)
            {
                Print("Daily levels set:");
                Print("High: ", DoubleToString(daily_high, _Digits));
                Print("Low: ", DoubleToString(daily_low, _Digits));
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Draw Levels on Chart                                            |
//+------------------------------------------------------------------+
void DrawLevels()
{
    // Remove existing lines
    ObjectDelete(0, "DailyHigh");
    ObjectDelete(0, "DailyLow");
    
    // Draw daily high line
    ObjectCreate(0, "DailyHigh", OBJ_HLINE, 0, 0, daily_high);
    ObjectSetInteger(0, "DailyHigh", OBJPROP_COLOR, clrRed);
    ObjectSetInteger(0, "DailyHigh", OBJPROP_STYLE, STYLE_DASH);
    ObjectSetInteger(0, "DailyHigh", OBJPROP_WIDTH, 2);
    ObjectSetString(0, "DailyHigh", OBJPROP_TEXT, "Daily High: " + DoubleToString(daily_high, _Digits));
    
    // Draw daily low line
    ObjectCreate(0, "DailyLow", OBJ_HLINE, 0, 0, daily_low);
    ObjectSetInteger(0, "DailyLow", OBJPROP_COLOR, clrBlue);
    ObjectSetInteger(0, "DailyLow", OBJPROP_STYLE, STYLE_DASH);
    ObjectSetInteger(0, "DailyLow", OBJPROP_WIDTH, 2);
    ObjectSetString(0, "DailyLow", OBJPROP_TEXT, "Daily Low: " + DoubleToString(daily_low, _Digits));
}

//+------------------------------------------------------------------+
//| Check for Breakouts                                             |
//+------------------------------------------------------------------+
void CheckBreakouts()
{
    if(!levels_set)
        return;

    double current_high = iHigh(_Symbol, _Period, 0);
    double current_low = iLow(_Symbol, _Period, 0);
    double current_close = iClose(_Symbol, _Period, 0);

    // Check for high breakout
    if(!high_broken && current_high > daily_high + breakout_distance)
    {
        high_broken = true;
        waiting_for_retest_high = true;
        breakout_time_high = TimeCurrent();

        if(InpEnableLogging)
            Print("Daily HIGH broken at: ", DoubleToString(current_high, _Digits),
                  " Level was: ", DoubleToString(daily_high, _Digits));
    }

    // Check for low breakout
    if(!low_broken && current_low < daily_low - breakout_distance)
    {
        low_broken = true;
        waiting_for_retest_low = true;
        breakout_time_low = TimeCurrent();

        if(InpEnableLogging)
            Print("Daily LOW broken at: ", DoubleToString(current_low, _Digits),
                  " Level was: ", DoubleToString(daily_low, _Digits));
    }
}

//+------------------------------------------------------------------+
//| Check for Retest Entries                                        |
//+------------------------------------------------------------------+
void CheckRetestEntries()
{
    if(!levels_set)
        return;

    // Skip if we already have a position
    if(PositionSelect(_Symbol))
        return;

    double current_high = iHigh(_Symbol, _Period, 0);
    double current_low = iLow(_Symbol, _Period, 0);
    double current_close = iClose(_Symbol, _Period, 0);

    // Check for retest of broken high (BUY setup)
    if(waiting_for_retest_high && high_broken)
    {
        // Price should come back to test the daily high level
        if(current_low <= daily_high + retest_distance && current_close > daily_high)
        {
            // Retest confirmed - place BUY order
            OpenBuyTrade();
            waiting_for_retest_high = false;

            if(InpEnableLogging)
                Print("HIGH retest confirmed - BUY signal at: ", DoubleToString(current_close, _Digits));
        }
    }

    // Check for retest of broken low (SELL setup)
    if(waiting_for_retest_low && low_broken)
    {
        // Price should come back to test the daily low level
        if(current_high >= daily_low - retest_distance && current_close < daily_low)
        {
            // Retest confirmed - place SELL order
            OpenSellTrade();
            waiting_for_retest_low = false;

            if(InpEnableLogging)
                Print("LOW retest confirmed - SELL signal at: ", DoubleToString(current_close, _Digits));
        }
    }
}

//+------------------------------------------------------------------+
//| Open Buy Trade                                                  |
//+------------------------------------------------------------------+
void OpenBuyTrade()
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    // Calculate position size
    double lot_size = CalculatePositionSize();
    if(lot_size <= 0)
        return;

    // Calculate stop loss and take profit
    double stop_loss = ask - (InpStopLossPips * pip_size);
    double take_profit = ask + (InpTakeProfitPips * pip_size);

    // Open BUY position
    if(trade.Buy(lot_size, _Symbol, ask, stop_loss, take_profit, InpTradeComment))
    {
        trades_today++;

        if(InpEnableLogging)
        {
            Print("BUY order opened:");
            Print("Entry: ", ask);
            Print("Stop Loss: ", stop_loss);
            Print("Take Profit: ", take_profit);
            Print("Lot Size: ", lot_size);
        }
    }
    else
    {
        Print("Failed to open BUY order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Open Sell Trade                                                 |
//+------------------------------------------------------------------+
void OpenSellTrade()
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    // Calculate position size
    double lot_size = CalculatePositionSize();
    if(lot_size <= 0)
        return;

    // Calculate stop loss and take profit
    double stop_loss = bid + (InpStopLossPips * pip_size);
    double take_profit = bid - (InpTakeProfitPips * pip_size);

    // Open SELL position
    if(trade.Sell(lot_size, _Symbol, bid, stop_loss, take_profit, InpTradeComment))
    {
        trades_today++;

        if(InpEnableLogging)
        {
            Print("SELL order opened:");
            Print("Entry: ", bid);
            Print("Stop Loss: ", stop_loss);
            Print("Take Profit: ", take_profit);
            Print("Lot Size: ", lot_size);
        }
    }
    else
    {
        Print("Failed to open SELL order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Calculate Position Size                                         |
//+------------------------------------------------------------------+
double CalculatePositionSize()
{
    double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = account_balance * InpRiskPercent / 100;

    // Calculate risk per pip
    double sl_distance = InpStopLossPips * pip_size;
    if(sl_distance <= 0)
        return 0;

    // Get contract size and tick value
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

    // Calculate position size
    double position_size = (risk_amount * tick_size) / (sl_distance * tick_value);

    // Normalize to lot step
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);

    position_size = MathFloor(position_size / lot_step) * lot_step;
    position_size = MathMax(position_size, min_lot);
    position_size = MathMin(position_size, max_lot);

    return position_size;
}

//+------------------------------------------------------------------+
//| Manage Positions                                                |
//+------------------------------------------------------------------+
void ManagePositions()
{
    if(!PositionSelect(_Symbol))
        return;

    if(!InpUseTrailingStop)
        return;

    double current_price = PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ?
                          SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                          SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    double entry_price = PositionGetDouble(POSITION_PRICE_OPEN);
    double current_sl = PositionGetDouble(POSITION_SL);
    double current_tp = PositionGetDouble(POSITION_TP);
    bool is_buy = PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY;

    double trailing_distance = InpTrailingStopPips * pip_size;
    double trailing_step = InpTrailingStepPips * pip_size;

    double new_sl = current_sl;

    if(is_buy)
    {
        // For BUY positions, trail stop loss upward
        double potential_sl = current_price - trailing_distance;

        // Only move SL up, and only if the move is significant
        if(potential_sl > current_sl + trailing_step)
        {
            new_sl = NormalizeDouble(potential_sl, _Digits);
        }
    }
    else
    {
        // For SELL positions, trail stop loss downward
        double potential_sl = current_price + trailing_distance;

        // Only move SL down, and only if the move is significant
        if(potential_sl < current_sl - trailing_step || current_sl == 0)
        {
            new_sl = NormalizeDouble(potential_sl, _Digits);
        }
    }

    // Update stop loss if changed
    if(new_sl != current_sl)
    {
        if(trade.PositionModify(_Symbol, new_sl, current_tp))
        {
            if(InpEnableLogging)
                Print("Trailing stop updated from ", current_sl, " to ", new_sl);
        }
    }
}
