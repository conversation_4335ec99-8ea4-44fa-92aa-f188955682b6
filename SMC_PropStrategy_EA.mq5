//+------------------------------------------------------------------+
//|                                           SMC_PropStrategy_EA.mq5 |
//|                                  Copyright 2024, Augment Agent    |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Augment Agent"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Smart Money Concepts Prop Strategy EA"
#property description "BOS/CHoCH + Order Blocks + FVG + Premium/Discount Zones"

//--- Include necessary libraries
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>

//--- Global objects
CTrade         trade;
CPositionInfo  position;

//+------------------------------------------------------------------+
//| Input Parameters                                                 |
//+------------------------------------------------------------------+
input group "=== STRATEGY SETTINGS ==="
input bool     InpEnableTrading = true;                    // Enable Trading
input int      InpMagicNumber = 987654;                    // Magic Number
input string   InpTradeComment = "SMC_PropStrategy";       // Trade Comment

input group "=== RISK MANAGEMENT ==="
input double   InpRiskPercent = 0.75;                      // Risk Per Trade (%)
input double   InpMaxDailyDD = 3.0;                        // Max Daily Drawdown (%)
input double   InpMaxTotalDD = 5.0;                        // Max Total Drawdown (%)
input int      InpMaxTradesPerDay = 2;                     // Max Trades Per Day
input double   InpMaxSpreadPips = 2.0;                     // Max Spread Filter (Pips)

input group "=== SMC SETTINGS ==="
input int      InpSwingLength = 10;                        // Swing Length for BOS/CHoCH
input int      InpOBLookback = 20;                         // Order Block Lookback Bars
input double   InpOBBufferPips = 2.0;                      // Order Block Buffer (Pips)
input bool     InpEnableFVGFilter = true;                  // Enable FVG Filter
input int      InpFVGLookback = 15;                        // FVG Lookback Bars
input bool     InpEnableLiquidityFilter = true;            // Enable EQH/EQL Filter

input group "=== TAKE PROFIT SETTINGS ==="
input bool     InpUseMultipleTP = true;                    // Use Multiple Take Profits
input double   InpTP1_RR = 1.0;                            // TP1 Risk:Reward Ratio
input double   InpTP2_RR = 2.0;                            // TP2 Risk:Reward Ratio
input double   InpTP3_RR = 3.0;                            // TP3 Risk:Reward Ratio
input double   InpTP1_Percent = 30.0;                      // TP1 Close Percentage
input double   InpTP2_Percent = 50.0;                      // TP2 Close Percentage
input double   InpTP3_Percent = 20.0;                      // TP3 Close Percentage

input group "=== TIME FILTER ==="
input int      InpTradingStartHour = 8;                    // Trading Start Hour (UTC)
input int      InpTradingEndHour = 17;                     // Trading End Hour (UTC)
input bool     InpCloseOnFriday = true;                    // Close All on Friday
input int      InpFridayCloseHour = 21;                    // Friday Close Hour (UTC)

input group "=== LOGGING ==="
input bool     InpEnableLogging = true;                    // Enable Logging
input bool     InpShowVisuals = true;                      // Show Visual Signals

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
// SMC Structure Variables
struct SwingPoint {
    datetime time;
    double price;
    bool is_high;
    int bar_index;
};

struct OrderBlock {
    datetime time;
    double high;
    double low;
    bool is_bullish;
    bool is_valid;
    int strength;
};

struct FairValueGap {
    datetime time;
    double upper;
    double lower;
    bool is_bullish;
    bool is_filled;
};

// Arrays for SMC structures
SwingPoint swing_points[];
OrderBlock order_blocks[];
FairValueGap fvg_gaps[];

// Market structure variables
bool bos_bullish, bos_bearish;
bool choch_bullish, choch_bearish;
double last_swing_high, last_swing_low;
datetime last_swing_high_time, last_swing_low_time;

// Premium/Discount zones
double premium_zone_start, discount_zone_end, equilibrium;

// Trading variables
double daily_start_balance;
double total_start_balance;
int trades_today;
datetime last_trade_date;
bool trading_allowed;

// Position management
struct TPLevel {
    ulong ticket;
    double volume;
    bool closed;
};
TPLevel tp_levels[3];

// Price calculation variables
double pip_size;
double ob_buffer_distance;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Set magic number
    trade.SetExpertMagicNumber(InpMagicNumber);
    
    // Calculate pip size
    pip_size = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    if(SymbolInfoInteger(_Symbol, SYMBOL_DIGITS) == 5 || SymbolInfoInteger(_Symbol, SYMBOL_DIGITS) == 3)
        pip_size *= 10;
    
    ob_buffer_distance = InpOBBufferPips * pip_size;
    
    // Initialize arrays
    ArrayResize(swing_points, 100);
    ArrayResize(order_blocks, 50);
    ArrayResize(fvg_gaps, 30);
    
    // Initialize variables
    daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    total_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    trades_today = 0;
    last_trade_date = 0;
    trading_allowed = true;
    
    bos_bullish = false;
    bos_bearish = false;
    choch_bullish = false;
    choch_bearish = false;
    
    last_swing_high = 0;
    last_swing_low = 0;
    
    // Initialize TP levels
    for(int i = 0; i < 3; i++)
    {
        tp_levels[i].ticket = 0;
        tp_levels[i].volume = 0;
        tp_levels[i].closed = false;
    }
    
    if(InpEnableLogging)
        Print("SMC Prop Strategy EA initialized on ", _Symbol, " ", EnumToString(_Period));
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Clean up visual objects
    if(InpShowVisuals)
    {
        ObjectsDeleteAll(0, "SMC_");
    }
    
    if(InpEnableLogging)
        Print("SMC Prop Strategy EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check if new bar
    static datetime last_bar_time = 0;
    datetime current_bar_time = iTime(_Symbol, _Period, 0);
    
    if(current_bar_time == last_bar_time)
        return;
    
    last_bar_time = current_bar_time;
    
    // Update daily tracking
    UpdateDailyTracking();
    
    // Check risk management
    if(!CheckRiskManagement())
        return;
    
    // Check time filter
    if(!IsValidTradingTime())
        return;
    
    // Check spread filter
    if(!CheckSpreadFilter())
        return;
    
    // Update SMC structures
    UpdateSwingPoints();
    DetectBOSandCHoCH();
    UpdateOrderBlocks();
    UpdateFairValueGaps();
    UpdatePremiumDiscountZones();
    
    // Check for entry signals
    CheckEntrySignals();
    
    // Manage existing positions
    ManagePositions();
    
    // Close positions on Friday
    if(InpCloseOnFriday)
        CheckFridayClose();
}

//+------------------------------------------------------------------+
//| Update Daily Tracking                                           |
//+------------------------------------------------------------------+
void UpdateDailyTracking()
{
    datetime current_date = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));

    if(last_trade_date != current_date)
    {
        trades_today = 0;
        daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
        last_trade_date = current_date;

        if(InpEnableLogging)
            Print("New trading day started. Balance: ", daily_start_balance);
    }
}

//+------------------------------------------------------------------+
//| Check Risk Management                                           |
//+------------------------------------------------------------------+
bool CheckRiskManagement()
{
    double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);

    // Check daily drawdown
    double daily_dd = (daily_start_balance - current_balance) / daily_start_balance * 100;
    if(daily_dd >= InpMaxDailyDD)
    {
        trading_allowed = false;
        if(InpEnableLogging)
            Print("Daily drawdown limit reached: ", daily_dd, "%");
        return false;
    }

    // Check total drawdown
    double total_dd = (total_start_balance - current_balance) / total_start_balance * 100;
    if(total_dd >= InpMaxTotalDD)
    {
        trading_allowed = false;
        if(InpEnableLogging)
            Print("Total drawdown limit reached: ", total_dd, "%");
        return false;
    }

    // Check max trades per day
    if(trades_today >= InpMaxTradesPerDay)
    {
        if(InpEnableLogging)
            Print("Maximum trades per day reached: ", trades_today);
        return false;
    }

    return InpEnableTrading && trading_allowed;
}

//+------------------------------------------------------------------+
//| Check Valid Trading Time                                        |
//+------------------------------------------------------------------+
bool IsValidTradingTime()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);

    // Don't trade on weekends
    if(dt.day_of_week == 0 || dt.day_of_week == 6)
        return false;

    // Check trading hours
    if(dt.hour < InpTradingStartHour || dt.hour >= InpTradingEndHour)
        return false;

    return true;
}

//+------------------------------------------------------------------+
//| Check Spread Filter                                             |
//+------------------------------------------------------------------+
bool CheckSpreadFilter()
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double spread_pips = (ask - bid) / pip_size;

    if(spread_pips > InpMaxSpreadPips)
    {
        if(InpEnableLogging)
            Print("Spread too wide: ", spread_pips, " pips");
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Update Swing Points                                             |
//+------------------------------------------------------------------+
void UpdateSwingPoints()
{
    double high_prices[], low_prices[];
    ArraySetAsSeries(high_prices, true);
    ArraySetAsSeries(low_prices, true);

    int bars_needed = InpSwingLength * 2 + 5;
    if(CopyHigh(_Symbol, _Period, 0, bars_needed, high_prices) < bars_needed ||
       CopyLow(_Symbol, _Period, 0, bars_needed, low_prices) < bars_needed)
        return;

    // Find swing highs and lows
    for(int i = InpSwingLength; i < bars_needed - InpSwingLength; i++)
    {
        bool is_swing_high = true;
        bool is_swing_low = true;

        // Check for swing high
        for(int j = i - InpSwingLength; j <= i + InpSwingLength; j++)
        {
            if(j != i && high_prices[j] >= high_prices[i])
            {
                is_swing_high = false;
                break;
            }
        }

        // Check for swing low
        for(int j = i - InpSwingLength; j <= i + InpSwingLength; j++)
        {
            if(j != i && low_prices[j] <= low_prices[i])
            {
                is_swing_low = false;
                break;
            }
        }

        // Store swing points
        if(is_swing_high)
        {
            SwingPoint sp;
            sp.time = iTime(_Symbol, _Period, i);
            sp.price = high_prices[i];
            sp.is_high = true;
            sp.bar_index = i;

            // Add to array (simplified - in production, manage array size)
            int size = ArraySize(swing_points);
            if(size < 100)
            {
                swing_points[size] = sp;
                ArrayResize(swing_points, size + 1);
            }

            // Update last swing high
            if(high_prices[i] > last_swing_high || last_swing_high == 0)
            {
                last_swing_high = high_prices[i];
                last_swing_high_time = sp.time;
            }
        }

        if(is_swing_low)
        {
            SwingPoint sp;
            sp.time = iTime(_Symbol, _Period, i);
            sp.price = low_prices[i];
            sp.is_high = false;
            sp.bar_index = i;

            // Add to array
            int size = ArraySize(swing_points);
            if(size < 100)
            {
                swing_points[size] = sp;
                ArrayResize(swing_points, size + 1);
            }

            // Update last swing low
            if(low_prices[i] < last_swing_low || last_swing_low == 0)
            {
                last_swing_low = low_prices[i];
                last_swing_low_time = sp.time;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Detect BOS and CHoCH                                            |
//+------------------------------------------------------------------+
void DetectBOSandCHoCH()
{
    double current_high = iHigh(_Symbol, _Period, 0);
    double current_low = iLow(_Symbol, _Period, 0);
    double prev_high = iHigh(_Symbol, _Period, 1);
    double prev_low = iLow(_Symbol, _Period, 1);

    static bool last_trend_bullish = false;
    static bool trend_established = false;

    // Reset signals
    bos_bullish = false;
    bos_bearish = false;
    choch_bullish = false;
    choch_bearish = false;

    // Detect Break of Structure (BOS) - continuation
    if(last_swing_high > 0 && current_high > last_swing_high)
    {
        if(last_trend_bullish || !trend_established)
        {
            bos_bullish = true;
            last_trend_bullish = true;
            trend_established = true;

            if(InpEnableLogging)
                Print("BOS Bullish detected - High broken: ", last_swing_high);
        }
        else
        {
            choch_bullish = true;
            last_trend_bullish = true;

            if(InpEnableLogging)
                Print("CHoCH Bullish detected - Trend change");
        }
    }

    if(last_swing_low > 0 && current_low < last_swing_low)
    {
        if(!last_trend_bullish || !trend_established)
        {
            bos_bearish = true;
            last_trend_bullish = false;
            trend_established = true;

            if(InpEnableLogging)
                Print("BOS Bearish detected - Low broken: ", last_swing_low);
        }
        else
        {
            choch_bearish = true;
            last_trend_bullish = false;

            if(InpEnableLogging)
                Print("CHoCH Bearish detected - Trend change");
        }
    }
}

//+------------------------------------------------------------------+
//| Update Order Blocks                                             |
//+------------------------------------------------------------------+
void UpdateOrderBlocks()
{
    double high_prices[], low_prices[], open_prices[], close_prices[];
    ArraySetAsSeries(high_prices, true);
    ArraySetAsSeries(low_prices, true);
    ArraySetAsSeries(open_prices, true);
    ArraySetAsSeries(close_prices, true);

    if(CopyHigh(_Symbol, _Period, 0, InpOBLookback, high_prices) < InpOBLookback ||
       CopyLow(_Symbol, _Period, 0, InpOBLookback, low_prices) < InpOBLookback ||
       CopyOpen(_Symbol, _Period, 0, InpOBLookback, open_prices) < InpOBLookback ||
       CopyClose(_Symbol, _Period, 0, InpOBLookback, close_prices) < InpOBLookback)
        return;

    // Look for order blocks after BOS/CHoCH
    if(bos_bullish || choch_bullish)
    {
        // Find last bearish candle before the break
        for(int i = 1; i < InpOBLookback; i++)
        {
            if(close_prices[i] < open_prices[i]) // Bearish candle
            {
                OrderBlock ob;
                ob.time = iTime(_Symbol, _Period, i);
                ob.high = high_prices[i];
                ob.low = low_prices[i];
                ob.is_bullish = true;
                ob.is_valid = true;
                ob.strength = CalculateOBStrength(i, true);

                // Add to order blocks array
                int size = ArraySize(order_blocks);
                if(size < 50)
                {
                    order_blocks[size] = ob;
                    ArrayResize(order_blocks, size + 1);
                }

                if(InpEnableLogging)
                    Print("Bullish Order Block created at: ", ob.low, " - ", ob.high);

                break;
            }
        }
    }

    if(bos_bearish || choch_bearish)
    {
        // Find last bullish candle before the break
        for(int i = 1; i < InpOBLookback; i++)
        {
            if(close_prices[i] > open_prices[i]) // Bullish candle
            {
                OrderBlock ob;
                ob.time = iTime(_Symbol, _Period, i);
                ob.high = high_prices[i];
                ob.low = low_prices[i];
                ob.is_bullish = false;
                ob.is_valid = true;
                ob.strength = CalculateOBStrength(i, false);

                // Add to order blocks array
                int size = ArraySize(order_blocks);
                if(size < 50)
                {
                    order_blocks[size] = ob;
                    ArrayResize(order_blocks, size + 1);
                }

                if(InpEnableLogging)
                    Print("Bearish Order Block created at: ", ob.low, " - ", ob.high);

                break;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Calculate Order Block Strength                                  |
//+------------------------------------------------------------------+
int CalculateOBStrength(int bar_index, bool is_bullish)
{
    double volume = iVolume(_Symbol, _Period, bar_index);
    double avg_volume = 0;

    // Calculate average volume
    for(int i = bar_index; i < bar_index + 10; i++)
    {
        avg_volume += iVolume(_Symbol, _Period, i);
    }
    avg_volume /= 10;

    // Strength based on volume and candle size
    double candle_size = MathAbs(iHigh(_Symbol, _Period, bar_index) - iLow(_Symbol, _Period, bar_index));
    double avg_candle_size = 0;

    for(int i = bar_index; i < bar_index + 10; i++)
    {
        avg_candle_size += MathAbs(iHigh(_Symbol, _Period, i) - iLow(_Symbol, _Period, i));
    }
    avg_candle_size /= 10;

    int strength = 1;
    if(volume > avg_volume * 1.5) strength++;
    if(candle_size > avg_candle_size * 1.5) strength++;

    return strength;
}

//+------------------------------------------------------------------+
//| Update Fair Value Gaps                                          |
//+------------------------------------------------------------------+
void UpdateFairValueGaps()
{
    if(!InpEnableFVGFilter)
        return;

    double high_prices[], low_prices[];
    ArraySetAsSeries(high_prices, true);
    ArraySetAsSeries(low_prices, true);

    if(CopyHigh(_Symbol, _Period, 0, InpFVGLookback, high_prices) < InpFVGLookback ||
       CopyLow(_Symbol, _Period, 0, InpFVGLookback, low_prices) < InpFVGLookback)
        return;

    // Look for FVG patterns (3-candle pattern)
    for(int i = 2; i < InpFVGLookback - 1; i++)
    {
        // Bullish FVG: Low[i-1] > High[i+1]
        if(low_prices[i-1] > high_prices[i+1])
        {
            FairValueGap fvg;
            fvg.time = iTime(_Symbol, _Period, i);
            fvg.upper = low_prices[i-1];
            fvg.lower = high_prices[i+1];
            fvg.is_bullish = true;
            fvg.is_filled = false;

            // Add to FVG array
            int size = ArraySize(fvg_gaps);
            if(size < 30)
            {
                fvg_gaps[size] = fvg;
                ArrayResize(fvg_gaps, size + 1);
            }

            if(InpEnableLogging)
                Print("Bullish FVG detected: ", fvg.lower, " - ", fvg.upper);
        }

        // Bearish FVG: High[i-1] < Low[i+1]
        if(high_prices[i-1] < low_prices[i+1])
        {
            FairValueGap fvg;
            fvg.time = iTime(_Symbol, _Period, i);
            fvg.upper = low_prices[i+1];
            fvg.lower = high_prices[i-1];
            fvg.is_bullish = false;
            fvg.is_filled = false;

            // Add to FVG array
            int size = ArraySize(fvg_gaps);
            if(size < 30)
            {
                fvg_gaps[size] = fvg;
                ArrayResize(fvg_gaps, size + 1);
            }

            if(InpEnableLogging)
                Print("Bearish FVG detected: ", fvg.lower, " - ", fvg.upper);
        }
    }

    // Check if existing FVGs are filled
    for(int i = 0; i < ArraySize(fvg_gaps); i++)
    {
        if(!fvg_gaps[i].is_filled)
        {
            double current_high = iHigh(_Symbol, _Period, 0);
            double current_low = iLow(_Symbol, _Period, 0);

            if(fvg_gaps[i].is_bullish)
            {
                if(current_low <= fvg_gaps[i].lower)
                    fvg_gaps[i].is_filled = true;
            }
            else
            {
                if(current_high >= fvg_gaps[i].upper)
                    fvg_gaps[i].is_filled = true;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Update Premium/Discount Zones                                   |
//+------------------------------------------------------------------+
void UpdatePremiumDiscountZones()
{
    if(last_swing_high > 0 && last_swing_low > 0)
    {
        equilibrium = (last_swing_high + last_swing_low) / 2;
        premium_zone_start = equilibrium + (last_swing_high - equilibrium) * 0.5;
        discount_zone_end = equilibrium - (equilibrium - last_swing_low) * 0.5;

        if(InpEnableLogging)
        {
            Print("Premium/Discount zones updated:");
            Print("Premium starts at: ", premium_zone_start);
            Print("Equilibrium: ", equilibrium);
            Print("Discount ends at: ", discount_zone_end);
        }
    }
}

//+------------------------------------------------------------------+
//| Check Entry Signals                                             |
//+------------------------------------------------------------------+
void CheckEntrySignals()
{
    if(PositionSelect(_Symbol))
        return;

    double current_price = iClose(_Symbol, _Period, 0);
    double current_high = iHigh(_Symbol, _Period, 0);
    double current_low = iLow(_Symbol, _Period, 0);

    // Check for LONG setup
    if((bos_bullish || choch_bullish) && IsInDiscountZone(current_price))
    {
        OrderBlock valid_ob = GetValidBullishOB(current_price);
        if(valid_ob.is_valid)
        {
            bool fvg_present = InpEnableFVGFilter ? IsFVGPresent(current_price, true) : true;
            bool liquidity_clear = InpEnableLiquidityFilter ? IsLiquidityClear(true) : true;

            if(fvg_present && liquidity_clear && IsPriceInOB(current_price, valid_ob))
            {
                if(InpEnableLogging)
                    Print("LONG setup confirmed - entering BUY trade");

                OpenBuyTrade(valid_ob);
            }
        }
    }

    // Check for SHORT setup
    if((bos_bearish || choch_bearish) && IsInPremiumZone(current_price))
    {
        OrderBlock valid_ob = GetValidBearishOB(current_price);
        if(valid_ob.is_valid)
        {
            bool fvg_present = InpEnableFVGFilter ? IsFVGPresent(current_price, false) : true;
            bool liquidity_clear = InpEnableLiquidityFilter ? IsLiquidityClear(false) : true;

            if(fvg_present && liquidity_clear && IsPriceInOB(current_price, valid_ob))
            {
                if(InpEnableLogging)
                    Print("SHORT setup confirmed - entering SELL trade");

                OpenSellTrade(valid_ob);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Helper Functions                                                |
//+------------------------------------------------------------------+
bool IsInDiscountZone(double price)
{
    return (price <= equilibrium && price >= discount_zone_end);
}

bool IsInPremiumZone(double price)
{
    return (price >= equilibrium && price <= premium_zone_start);
}

OrderBlock GetValidBullishOB(double current_price)
{
    OrderBlock invalid_ob;
    invalid_ob.is_valid = false;

    for(int i = ArraySize(order_blocks) - 1; i >= 0; i--)
    {
        if(order_blocks[i].is_bullish && order_blocks[i].is_valid)
        {
            if(current_price >= order_blocks[i].low - ob_buffer_distance &&
               current_price <= order_blocks[i].high + ob_buffer_distance)
            {
                return order_blocks[i];
            }
        }
    }

    return invalid_ob;
}

OrderBlock GetValidBearishOB(double current_price)
{
    OrderBlock invalid_ob;
    invalid_ob.is_valid = false;

    for(int i = ArraySize(order_blocks) - 1; i >= 0; i--)
    {
        if(!order_blocks[i].is_bullish && order_blocks[i].is_valid)
        {
            if(current_price >= order_blocks[i].low - ob_buffer_distance &&
               current_price <= order_blocks[i].high + ob_buffer_distance)
            {
                return order_blocks[i];
            }
        }
    }

    return invalid_ob;
}

bool IsFVGPresent(double price, bool is_bullish)
{
    for(int i = 0; i < ArraySize(fvg_gaps); i++)
    {
        if(!fvg_gaps[i].is_filled && fvg_gaps[i].is_bullish == is_bullish)
        {
            if(price >= fvg_gaps[i].lower && price <= fvg_gaps[i].upper)
                return true;
        }
    }
    return false;
}

bool IsLiquidityClear(bool is_bullish)
{
    // Simplified liquidity check - in production, implement EQH/EQL detection
    return true;
}

bool IsPriceInOB(double price, OrderBlock ob)
{
    return (price >= ob.low - ob_buffer_distance && price <= ob.high + ob_buffer_distance);
}

//+------------------------------------------------------------------+
//| Open Buy Trade                                                  |
//+------------------------------------------------------------------+
void OpenBuyTrade(OrderBlock ob)
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    // Calculate position size
    double lot_size = CalculatePositionSize(ob);
    if(lot_size <= 0)
        return;

    // Calculate stop loss (below order block)
    double stop_loss = ob.low - ob_buffer_distance;

    // Calculate take profits
    double sl_distance = ask - stop_loss;
    double tp1 = ask + (sl_distance * InpTP1_RR);
    double tp2 = ask + (sl_distance * InpTP2_RR);
    double tp3 = ask + (sl_distance * InpTP3_RR);

    // Open position with first TP
    if(trade.Buy(lot_size, _Symbol, ask, stop_loss, tp1, InpTradeComment))
    {
        ulong ticket = trade.ResultOrder();
        trades_today++;

        // Store TP levels for management
        if(InpUseMultipleTP)
        {
            tp_levels[0].ticket = ticket;
            tp_levels[0].volume = lot_size * InpTP1_Percent / 100;
            tp_levels[0].closed = false;

            tp_levels[1].ticket = ticket;
            tp_levels[1].volume = lot_size * InpTP2_Percent / 100;
            tp_levels[1].closed = false;

            tp_levels[2].ticket = ticket;
            tp_levels[2].volume = lot_size * InpTP3_Percent / 100;
            tp_levels[2].closed = false;
        }

        if(InpEnableLogging)
        {
            Print("BUY order opened:");
            Print("Entry: ", ask);
            Print("Stop Loss: ", stop_loss);
            Print("TP1: ", tp1, " TP2: ", tp2, " TP3: ", tp3);
            Print("Lot Size: ", lot_size);
        }
    }
    else
    {
        Print("Failed to open BUY order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Open Sell Trade                                                 |
//+------------------------------------------------------------------+
void OpenSellTrade(OrderBlock ob)
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    // Calculate position size
    double lot_size = CalculatePositionSize(ob);
    if(lot_size <= 0)
        return;

    // Calculate stop loss (above order block)
    double stop_loss = ob.high + ob_buffer_distance;

    // Calculate take profits
    double sl_distance = stop_loss - bid;
    double tp1 = bid - (sl_distance * InpTP1_RR);
    double tp2 = bid - (sl_distance * InpTP2_RR);
    double tp3 = bid - (sl_distance * InpTP3_RR);

    // Open position with first TP
    if(trade.Sell(lot_size, _Symbol, bid, stop_loss, tp1, InpTradeComment))
    {
        ulong ticket = trade.ResultOrder();
        trades_today++;

        // Store TP levels for management
        if(InpUseMultipleTP)
        {
            tp_levels[0].ticket = ticket;
            tp_levels[0].volume = lot_size * InpTP1_Percent / 100;
            tp_levels[0].closed = false;

            tp_levels[1].ticket = ticket;
            tp_levels[1].volume = lot_size * InpTP2_Percent / 100;
            tp_levels[1].closed = false;

            tp_levels[2].ticket = ticket;
            tp_levels[2].volume = lot_size * InpTP3_Percent / 100;
            tp_levels[2].closed = false;
        }

        if(InpEnableLogging)
        {
            Print("SELL order opened:");
            Print("Entry: ", bid);
            Print("Stop Loss: ", stop_loss);
            Print("TP1: ", tp1, " TP2: ", tp2, " TP3: ", tp3);
            Print("Lot Size: ", lot_size);
        }
    }
    else
    {
        Print("Failed to open SELL order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Calculate Position Size                                         |
//+------------------------------------------------------------------+
double CalculatePositionSize(OrderBlock ob)
{
    double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = account_balance * InpRiskPercent / 100;

    // Calculate SL distance
    double current_price = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) + SymbolInfoDouble(_Symbol, SYMBOL_BID)) / 2;
    double sl_distance;

    if(ob.is_bullish)
        sl_distance = current_price - (ob.low - ob_buffer_distance);
    else
        sl_distance = (ob.high + ob_buffer_distance) - current_price;

    if(sl_distance <= 0)
        return 0;

    // Calculate position size
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

    double position_size = (risk_amount * tick_size) / (sl_distance * tick_value);

    // Normalize to lot step
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);

    position_size = MathFloor(position_size / lot_step) * lot_step;
    position_size = MathMax(position_size, min_lot);
    position_size = MathMin(position_size, max_lot);

    return position_size;
}

//+------------------------------------------------------------------+
//| Manage Positions                                                |
//+------------------------------------------------------------------+
void ManagePositions()
{
    if(!PositionSelect(_Symbol) || !InpUseMultipleTP)
        return;

    double current_price = PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ?
                          SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                          SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    double entry_price = PositionGetDouble(POSITION_PRICE_OPEN);
    double stop_loss = PositionGetDouble(POSITION_SL);
    bool is_buy = PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY;

    // Calculate profit in R multiples
    double sl_distance = is_buy ? (entry_price - stop_loss) : (stop_loss - entry_price);
    double current_profit = is_buy ? (current_price - entry_price) : (entry_price - current_price);
    double r_multiple = (sl_distance > 0) ? current_profit / sl_distance : 0;

    // TP1 at 1R
    if(r_multiple >= InpTP1_RR && !tp_levels[0].closed)
    {
        double close_volume = tp_levels[0].volume;
        if(trade.PositionClosePartial(_Symbol, close_volume))
        {
            tp_levels[0].closed = true;

            // Move SL to breakeven
            trade.PositionModify(_Symbol, entry_price, PositionGetDouble(POSITION_TP));

            if(InpEnableLogging)
                Print("TP1 hit - Closed ", InpTP1_Percent, "% at ", r_multiple, "R, SL moved to breakeven");
        }
    }

    // TP2 at 2R
    if(r_multiple >= InpTP2_RR && !tp_levels[1].closed)
    {
        double close_volume = tp_levels[1].volume;
        if(trade.PositionClosePartial(_Symbol, close_volume))
        {
            tp_levels[1].closed = true;

            if(InpEnableLogging)
                Print("TP2 hit - Closed ", InpTP2_Percent, "% at ", r_multiple, "R");
        }
    }

    // TP3 at 3R
    if(r_multiple >= InpTP3_RR && !tp_levels[2].closed)
    {
        if(trade.PositionClose(_Symbol))
        {
            tp_levels[2].closed = true;

            // Reset TP levels
            for(int i = 0; i < 3; i++)
            {
                tp_levels[i].ticket = 0;
                tp_levels[i].volume = 0;
                tp_levels[i].closed = false;
            }

            if(InpEnableLogging)
                Print("TP3 hit - Closed remaining position at ", r_multiple, "R");
        }
    }
}

//+------------------------------------------------------------------+
//| Check Friday Close                                              |
//+------------------------------------------------------------------+
void CheckFridayClose()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);

    if(dt.day_of_week == 5 && dt.hour >= InpFridayCloseHour) // Friday
    {
        if(PositionSelect(_Symbol))
        {
            trade.PositionClose(_Symbol);

            // Reset TP levels
            for(int i = 0; i < 3; i++)
            {
                tp_levels[i].ticket = 0;
                tp_levels[i].volume = 0;
                tp_levels[i].closed = false;
            }

            if(InpEnableLogging)
                Print("Position closed due to Friday close rule");
        }
    }
}

//+------------------------------------------------------------------+
//| OnTrade Event Handler                                           |
//+------------------------------------------------------------------+
void OnTrade()
{
    if(InpEnableLogging)
    {
        HistorySelect(TimeCurrent() - 3600, TimeCurrent());

        for(int i = HistoryDealsTotal() - 1; i >= 0; i--)
        {
            ulong deal_ticket = HistoryDealGetTicket(i);
            if(deal_ticket > 0)
            {
                string symbol = HistoryDealGetString(deal_ticket, DEAL_SYMBOL);
                long magic = HistoryDealGetInteger(deal_ticket, DEAL_MAGIC);

                if(symbol == _Symbol && magic == InpMagicNumber)
                {
                    ENUM_DEAL_TYPE deal_type = (ENUM_DEAL_TYPE)HistoryDealGetInteger(deal_ticket, DEAL_TYPE);
                    double deal_profit = HistoryDealGetDouble(deal_ticket, DEAL_PROFIT);

                    Print("Trade completed - Type: ", EnumToString(deal_type), " Profit: ", deal_profit);
                    break;
                }
            }
        }
    }
}
