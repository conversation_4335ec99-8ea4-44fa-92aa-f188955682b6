//+------------------------------------------------------------------+
//|                                        Test_EA_Compilation.mq5 |
//|                                  Copyright 2024, Augment Agent |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Augment Agent"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Test script to validate MultiIndicator EA compilation"

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== MultiIndicator EA Compilation Test ===");
    
    // Test basic MT5 functions
    Print("Current Symbol: ", _Symbol);
    Print("Current Timeframe: ", EnumToString(_Period));
    Print("Account Balance: ", AccountInfoDouble(ACCOUNT_BALANCE));
    Print("Account Currency: ", AccountInfoString(ACCOUNT_CURRENCY));
    
    // Test indicator creation
    int atr_handle = iATR(_Symbol, _Period, 10);
    int ma_handle = iMA(_Symbol, _Period, 12, 0, MODE_EMA, PRICE_CLOSE);
    
    if(atr_handle != INVALID_HANDLE && ma_handle != INVALID_HANDLE)
    {
        Print("✅ Indicator handles created successfully");
        IndicatorRelease(atr_handle);
        IndicatorRelease(ma_handle);
    }
    else
    {
        Print("❌ Failed to create indicator handles");
    }
    
    // Test price data access
    double high[], low[], close[], volume[];
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);
    ArraySetAsSeries(volume, true);
    
    if(CopyHigh(_Symbol, _Period, 0, 10, high) == 10 &&
       CopyLow(_Symbol, _Period, 0, 10, low) == 10 &&
       CopyClose(_Symbol, _Period, 0, 10, close) == 10 &&
       CopyTickVolume(_Symbol, _Period, 0, 10, volume) == 10)
    {
        Print("✅ Price data access successful");
        Print("Current High: ", high[0]);
        Print("Current Low: ", low[0]);
        Print("Current Close: ", close[0]);
        Print("Current Volume: ", volume[0]);
    }
    else
    {
        Print("❌ Failed to access price data");
    }
    
    // Test trading functions
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double spread = ask - bid;
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    
    Print("✅ Market data access successful");
    Print("Ask: ", ask);
    Print("Bid: ", bid);
    Print("Spread: ", spread / point, " points");
    
    // Test time functions
    datetime current_time = TimeCurrent();
    MqlDateTime dt;
    TimeToStruct(current_time, dt);
    
    Print("✅ Time functions working");
    Print("Current Time: ", TimeToString(current_time));
    Print("Day of Week: ", dt.day_of_week);
    Print("Hour: ", dt.hour);
    
    // Test mathematical functions
    double test_array[] = {1.1234, 2.5678, 3.9012};
    double sum = 0;
    for(int i = 0; i < ArraySize(test_array); i++)
    {
        sum += test_array[i];
    }
    double average = sum / ArraySize(test_array);
    
    Print("✅ Mathematical functions working");
    Print("Array Sum: ", sum);
    Print("Array Average: ", average);
    
    // Test string functions
    string test_string = "08:00";
    string parts[];
    int count = StringSplit(test_string, ':', parts);
    
    if(count == 2)
    {
        Print("✅ String functions working");
        Print("Time parts: ", parts[0], ":", parts[1]);
    }
    else
    {
        Print("❌ String functions failed");
    }
    
    Print("=== Test Completed ===");
    Print("If all tests show ✅, the EA should compile and run correctly.");
    Print("If any tests show ❌, check your MT5 installation and symbol data.");
}
