//+------------------------------------------------------------------+
//|                                    MultiIndicator_PropFirm_EA.mq5 |
//|                                  Copyright 2024, Augment Agent    |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Augment Agent"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "4-Indicator Strategy EA: Supertrend + Zero Lag MACD + Volume Delta + Trendlines"
#property description "Prop Firm Compliant: 0.5% risk per trade, 3% daily DD, 5% total DD"

//--- Include necessary libraries
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>

//--- Global objects
CTrade         trade;
CPositionInfo  position;
COrderInfo     order;

//+------------------------------------------------------------------+
//| Input Parameters                                                 |
//+------------------------------------------------------------------+
input group "=== STRATEGY SETTINGS ==="
input bool     InpEnableTrading = true;                    // Enable Trading
input int      InpMagicNumber = 123456;                    // Magic Number
input string   InpTradeComment = "MultiIndicator_EA";      // Trade Comment

input group "=== SUPERTREND SETTINGS ==="
input int      InpSTR_Period = 10;                         // Supertrend ATR Period
input double   InpSTR_Factor = 3.0;                        // Supertrend Factor

input group "=== ZERO LAG MACD SETTINGS ==="
input int      InpMACD_FastLength = 12;                    // MACD Fast Length
input int      InpMACD_SlowLength = 26;                    // MACD Slow Length  
input int      InpMACD_SignalLength = 9;                   // MACD Signal Length
input bool     InpMACD_UseEMA = true;                      // Use EMA (otherwise SMA)

input group "=== VOLUME DELTA SETTINGS ==="
input int      InpVD_EMAFast = 12;                         // Volume Delta Fast EMA
input int      InpVD_EMASlow = 26;                         // Volume Delta Slow EMA
input double   InpVD_Threshold = 55.0;                     // Volume Delta Threshold %

input group "=== TRENDLINE SETTINGS ==="
input int      InpTL_LeftBars = 30;                        // Trendline Left Bars
input int      InpTL_RightBars = 30;                       // Trendline Right Bars
input double   InpTL_ProximityPips = 10.0;                 // Trendline Proximity (Pips)

input group "=== RISK MANAGEMENT ==="
input double   InpRiskPercent = 0.5;                       // Risk Per Trade (%)
input double   InpMaxDailyDD = 3.0;                        // Max Daily Drawdown (%)
input double   InpMaxTotalDD = 5.0;                        // Max Total Drawdown (%)
input int      InpMaxTradesPerDay = 3;                     // Max Trades Per Day
input bool     InpCloseOnFriday = true;                    // Close All on Friday

input group "=== TAKE PROFIT SETTINGS ==="
input double   InpTP1_Ratio = 1.0;                         // TP1 Risk:Reward Ratio
input double   InpTP2_Ratio = 2.0;                         // TP2 Risk:Reward Ratio  
input double   InpTP3_Ratio = 3.0;                         // TP3 Risk:Reward Ratio
input double   InpTP1_Percent = 30.0;                      // TP1 Position Close %
input double   InpTP2_Percent = 40.0;                      // TP2 Position Close %
input double   InpTP3_Percent = 30.0;                      // TP3 Position Close %

input group "=== SESSION FILTERING ==="
input bool     InpLondonSession = true;                    // Trade London Session
input bool     InpNewYorkSession = true;                   // Trade New York Session
input string   InpLondonStart = "08:00";                   // London Session Start
input string   InpLondonEnd = "17:00";                     // London Session End
input string   InpNewYorkStart = "13:00";                  // New York Session Start  
input string   InpNewYorkEnd = "22:00";                    // New York Session End

input group "=== LOGGING ==="
input bool     InpEnableLogging = true;                    // Enable Detailed Logging
input bool     InpLogSignals = true;                       // Log Signal Detection
input bool     InpLogTrades = true;                        // Log Trade Execution

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
// Indicator handles
int handle_atr;
int handle_ma_fast_vd, handle_ma_slow_vd;

// Indicator buffers
double atr_buffer[];
double ma_fast_vd[], ma_slow_vd[];

// Trading variables
double daily_start_balance;
double total_start_balance;
int trades_today;
datetime last_trade_date;
bool trading_allowed;

// Signal variables
bool supertrend_bullish, supertrend_bearish;
bool macd_bullish, macd_bearish;
bool volume_delta_bullish, volume_delta_bearish;
bool trendline_support, trendline_resistance;

// Enhanced signal variables for better detection
bool supertrend_uptrend, supertrend_downtrend;
bool macd_above_signal, macd_below_signal;
bool volume_buy_dominance, volume_sell_dominance;
bool price_near_support, price_near_resistance;

// Signal persistence counters
int supertrend_bull_count, supertrend_bear_count;
int macd_bull_count, macd_bear_count;
int volume_bull_count, volume_bear_count;
int trendline_support_count, trendline_resistance_count;

// Enhanced signal variables for better detection
bool supertrend_uptrend, supertrend_downtrend;
bool macd_above_signal, macd_below_signal;
bool volume_buy_dominance, volume_sell_dominance;
bool price_near_support, price_near_resistance;

// Signal persistence counters
int supertrend_bull_count, supertrend_bear_count;
int macd_bull_count, macd_bear_count;
int volume_bull_count, volume_bear_count;
int trendline_support_count, trendline_resistance_count;

// Trendline variables
struct PivotPoint {
    datetime time;
    double price;
    bool is_high;
};
PivotPoint pivot_points[];

// Position management
struct PositionLevel {
    ulong ticket;
    double volume;
    bool closed;
};
PositionLevel tp_levels[3];



//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release indicator handles
    if(handle_atr != INVALID_HANDLE) IndicatorRelease(handle_atr);
    if(handle_ma_fast_vd != INVALID_HANDLE) IndicatorRelease(handle_ma_fast_vd);
    if(handle_ma_slow_vd != INVALID_HANDLE) IndicatorRelease(handle_ma_slow_vd);
    
    if(InpEnableLogging)
        Print("MultiIndicator EA deinitialized. Reason: ", reason);
}



//+------------------------------------------------------------------+
//| Update Daily Tracking                                           |
//+------------------------------------------------------------------+
void UpdateDailyTracking()
{
    datetime current_date = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));

    if(last_trade_date != current_date)
    {
        // New day - reset counters
        trades_today = 0;
        daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
        last_trade_date = current_date;

        if(InpEnableLogging)
            Print("New trading day started. Balance: ", daily_start_balance);
    }
}

//+------------------------------------------------------------------+
//| Check Risk Management                                           |
//+------------------------------------------------------------------+
bool CheckRiskManagement()
{
    double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);

    // Check daily drawdown
    double daily_dd = (daily_start_balance - current_balance) / daily_start_balance * 100;
    if(daily_dd >= InpMaxDailyDD)
    {
        trading_allowed = false;
        if(InpEnableLogging)
            Print("Daily drawdown limit reached: ", daily_dd, "%");
        return false;
    }

    // Check total drawdown
    double total_dd = (total_start_balance - current_balance) / total_start_balance * 100;
    if(total_dd >= InpMaxTotalDD)
    {
        trading_allowed = false;
        if(InpEnableLogging)
            Print("Total drawdown limit reached: ", total_dd, "%");
        return false;
    }

    // Check max trades per day
    if(trades_today >= InpMaxTradesPerDay)
    {
        if(InpEnableLogging)
            Print("Maximum trades per day reached: ", trades_today);
        return false;
    }

    // Check if trading is enabled
    if(!InpEnableTrading || !trading_allowed)
        return false;

    return true;
}

//+------------------------------------------------------------------+
//| Check Session Activity                                          |
//+------------------------------------------------------------------+
bool IsSessionActive()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);

    int current_hour = dt.hour;
    int current_minute = dt.min;
    int current_time = current_hour * 100 + current_minute;

    // Parse session times
    string london_start_parts[];
    string london_end_parts[];
    string ny_start_parts[];
    string ny_end_parts[];

    StringSplit(InpLondonStart, ':', london_start_parts);
    StringSplit(InpLondonEnd, ':', london_end_parts);
    StringSplit(InpNewYorkStart, ':', ny_start_parts);
    StringSplit(InpNewYorkEnd, ':', ny_end_parts);

    int london_start = (int)StringToInteger(london_start_parts[0]) * 100 + (int)StringToInteger(london_start_parts[1]);
    int london_end = (int)StringToInteger(london_end_parts[0]) * 100 + (int)StringToInteger(london_end_parts[1]);
    int ny_start = (int)StringToInteger(ny_start_parts[0]) * 100 + (int)StringToInteger(ny_start_parts[1]);
    int ny_end = (int)StringToInteger(ny_end_parts[0]) * 100 + (int)StringToInteger(ny_end_parts[1]);

    bool london_active = InpLondonSession && (current_time >= london_start && current_time <= london_end);
    bool ny_active = InpNewYorkSession && (current_time >= ny_start && current_time <= ny_end);

    // Don't trade on weekends
    if(dt.day_of_week == 0 || dt.day_of_week == 6)
        return false;

    // Close all positions on Friday if enabled
    if(InpCloseOnFriday && dt.day_of_week == 5 && current_time >= 2100)
    {
        CloseAllPositions();
        return false;
    }

    return (london_active || ny_active);
}

//+------------------------------------------------------------------+
//| Update Indicators                                               |
//+------------------------------------------------------------------+
void UpdateIndicators()
{
    // Copy indicator data
    if(CopyBuffer(handle_atr, 0, 0, 3, atr_buffer) < 3)
        return;
    if(CopyBuffer(handle_ma_fast_vd, 0, 0, 3, ma_fast_vd) < 3)
        return;
    if(CopyBuffer(handle_ma_slow_vd, 0, 0, 3, ma_slow_vd) < 3)
        return;

    // Update Supertrend
    UpdateSupertrend();

    // Update Zero Lag MACD
    UpdateZeroLagMACD();

    // Update Volume Delta
    UpdateVolumeDelta();

    // Update Trendlines
    UpdateTrendlines();
}

//+------------------------------------------------------------------+
//| Update Supertrend Indicator                                     |
//+------------------------------------------------------------------+
void UpdateSupertrend()
{
    double high_prices[], low_prices[], close_prices[];
    ArraySetAsSeries(high_prices, true);
    ArraySetAsSeries(low_prices, true);
    ArraySetAsSeries(close_prices, true);

    if(CopyHigh(_Symbol, _Period, 0, 3, high_prices) < 3 ||
       CopyLow(_Symbol, _Period, 0, 3, low_prices) < 3 ||
       CopyClose(_Symbol, _Period, 0, 3, close_prices) < 3)
        return;

    // Calculate Supertrend
    double hl2_0 = (high_prices[0] + low_prices[0]) / 2;
    double hl2_1 = (high_prices[1] + low_prices[1]) / 2;

    double upper_band_0 = hl2_0 + (InpSTR_Factor * atr_buffer[0]);
    double lower_band_0 = hl2_0 - (InpSTR_Factor * atr_buffer[0]);
    double upper_band_1 = hl2_1 + (InpSTR_Factor * atr_buffer[1]);
    double lower_band_1 = hl2_1 - (InpSTR_Factor * atr_buffer[1]);

    static double supertrend_prev = 0;
    static int direction_prev = 1;

    // Calculate final upper and lower bands
    double final_upper_band = (upper_band_0 < upper_band_1 || close_prices[1] > upper_band_1) ? upper_band_0 : upper_band_1;
    double final_lower_band = (lower_band_0 > lower_band_1 || close_prices[1] < lower_band_1) ? lower_band_0 : lower_band_1;

    // Determine direction
    int direction = (close_prices[0] <= final_lower_band) ? -1 : (close_prices[0] >= final_upper_band) ? 1 : direction_prev;

    // Calculate Supertrend value
    double supertrend = (direction == 1) ? final_lower_band : final_upper_band;

    // Update signals - Both trend change AND trend state
    supertrend_bullish = (direction == 1 && direction_prev == -1); // Trend change signal
    supertrend_bearish = (direction == -1 && direction_prev == 1); // Trend change signal

    // Add trend state signals for better detection
    bool supertrend_uptrend = (direction == 1);
    bool supertrend_downtrend = (direction == -1);

    // Enhanced bullish/bearish conditions (trend change OR strong trend state)
    if(supertrend_uptrend && close_prices[0] > supertrend)
        supertrend_bullish = true;
    if(supertrend_downtrend && close_prices[0] < supertrend)
        supertrend_bearish = true;

    // Store for next calculation
    supertrend_prev = supertrend;
    direction_prev = direction;

    if(InpLogSignals && InpEnableLogging)
    {
        if(supertrend_bullish)
            Print("SUPERTREND: Bullish signal detected");
        if(supertrend_bearish)
            Print("SUPERTREND: Bearish signal detected");
    }
}

//+------------------------------------------------------------------+
//| Update Zero Lag MACD Indicator                                  |
//+------------------------------------------------------------------+
void UpdateZeroLagMACD()
{
    double close_prices[];
    ArraySetAsSeries(close_prices, true);

    if(CopyClose(_Symbol, _Period, 0, 50, close_prices) < 50)
        return;

    // Calculate Zero Lag EMAs for fast line
    double ma1_fast = 0, ma2_fast = 0;
    if(InpMACD_UseEMA)
    {
        ma1_fast = CalculateEMA(close_prices, InpMACD_FastLength, 0);
        ma2_fast = CalculateEMA(close_prices, InpMACD_FastLength, 1); // Previous EMA
    }
    else
    {
        ma1_fast = CalculateSMA(close_prices, InpMACD_FastLength, 0);
        ma2_fast = CalculateSMA(close_prices, InpMACD_FastLength, 1);
    }

    double zerolag_fast = (2 * ma1_fast) - ma2_fast;

    // Calculate Zero Lag EMAs for slow line
    double ma1_slow = 0, ma2_slow = 0;
    if(InpMACD_UseEMA)
    {
        ma1_slow = CalculateEMA(close_prices, InpMACD_SlowLength, 0);
        ma2_slow = CalculateEMA(close_prices, InpMACD_SlowLength, 1);
    }
    else
    {
        ma1_slow = CalculateSMA(close_prices, InpMACD_SlowLength, 0);
        ma2_slow = CalculateSMA(close_prices, InpMACD_SlowLength, 1);
    }

    double zerolag_slow = (2 * ma1_slow) - ma2_slow;

    // Calculate MACD line
    double macd_line = zerolag_fast - zerolag_slow;

    // Calculate Signal line (Zero Lag)
    static double macd_history[50];
    static bool macd_initialized = false;

    if(!macd_initialized)
    {
        for(int i = 0; i < 50; i++)
            macd_history[i] = macd_line;
        macd_initialized = true;
    }

    // Shift history
    for(int i = 49; i > 0; i--)
        macd_history[i] = macd_history[i-1];
    macd_history[0] = macd_line;

    double signal_ema1 = CalculateEMAFromArray(macd_history, InpMACD_SignalLength, 0);
    double signal_ema2 = CalculateEMAFromArray(macd_history, InpMACD_SignalLength, 1);
    double signal_line = (2 * signal_ema1) - signal_ema2;

    // Calculate histogram
    double histogram = macd_line - signal_line;

    // Previous values for crossover detection
    static double prev_macd_line = 0;
    static double prev_signal_line = 0;
    static double prev_histogram = 0;

    // Detect crossovers
    bool macd_cross_up = (macd_line > signal_line && prev_macd_line <= prev_signal_line);
    bool macd_cross_down = (macd_line < signal_line && prev_macd_line >= prev_signal_line);
    bool histogram_positive = histogram > 0;
    bool histogram_negative = histogram < 0;
    bool histogram_turning_positive = (histogram > 0 && prev_histogram <= 0);
    bool histogram_turning_negative = (histogram < 0 && prev_histogram >= 0);

    // Update signals
    macd_bullish = macd_cross_up && histogram_turning_positive;
    macd_bearish = macd_cross_down && histogram_turning_negative;

    // Store previous values
    prev_macd_line = macd_line;
    prev_signal_line = signal_line;
    prev_histogram = histogram;

    if(InpLogSignals && InpEnableLogging)
    {
        if(macd_bullish)
            Print("ZERO LAG MACD: Bullish signal detected - MACD: ", macd_line, " Signal: ", signal_line, " Hist: ", histogram);
        if(macd_bearish)
            Print("ZERO LAG MACD: Bearish signal detected - MACD: ", macd_line, " Signal: ", signal_line, " Hist: ", histogram);
    }
}

//+------------------------------------------------------------------+
//| Calculate EMA                                                   |
//+------------------------------------------------------------------+
double CalculateEMA(const double &prices[], int period, int shift)
{
    if(ArraySize(prices) < period + shift)
        return 0;

    double alpha = 2.0 / (period + 1);
    double ema = prices[shift];

    for(int i = shift + 1; i < period + shift; i++)
    {
        ema = alpha * prices[i] + (1 - alpha) * ema;
    }

    return ema;
}

//+------------------------------------------------------------------+
//| Calculate SMA                                                   |
//+------------------------------------------------------------------+
double CalculateSMA(const double &prices[], int period, int shift)
{
    if(ArraySize(prices) < period + shift)
        return 0;

    double sum = 0;
    for(int i = shift; i < period + shift; i++)
    {
        sum += prices[i];
    }

    return sum / period;
}

//+------------------------------------------------------------------+
//| Calculate EMA from Array                                        |
//+------------------------------------------------------------------+
double CalculateEMAFromArray(const double &data[], int period, int shift)
{
    if(ArraySize(data) < period + shift)
        return 0;

    double alpha = 2.0 / (period + 1);
    double ema = data[shift];

    for(int i = shift + 1; i < period + shift; i++)
    {
        ema = alpha * data[i] + (1 - alpha) * ema;
    }

    return ema;
}

//+------------------------------------------------------------------+
//| Update Volume Delta Analysis                                    |
//+------------------------------------------------------------------+
void UpdateVolumeDelta()
{
    double high_prices[], low_prices[], close_prices[];
    long tick_volumes[];
    ArraySetAsSeries(high_prices, true);
    ArraySetAsSeries(low_prices, true);
    ArraySetAsSeries(close_prices, true);
    ArraySetAsSeries(tick_volumes, true);

    if(CopyHigh(_Symbol, _Period, 0, 3, high_prices) < 3 ||
       CopyLow(_Symbol, _Period, 0, 3, low_prices) < 3 ||
       CopyClose(_Symbol, _Period, 0, 3, close_prices) < 3 ||
       CopyTickVolume(_Symbol, _Period, 0, 3, tick_volumes) < 3)
        return;

    // Calculate buy and sell volumes using the Pine Script formula
    double buy_volume = 0, sell_volume = 0;
    double range = high_prices[0] - low_prices[0];

    if(range > 0)
    {
        // Buy volume = volume * (close - low) / (high - low)
        buy_volume = (double)tick_volumes[0] * (close_prices[0] - low_prices[0]) / range;
        sell_volume = (double)tick_volumes[0] - buy_volume;
    }
    else
    {
        // If no range, split volume equally
        buy_volume = (double)tick_volumes[0] / 2;
        sell_volume = (double)tick_volumes[0] / 2;
    }

    // Calculate percentages
    double total_volume = buy_volume + sell_volume;
    double buy_percent = (total_volume > 0) ? (buy_volume / total_volume) * 100 : 50;
    double sell_percent = 100 - buy_percent;

    // Check EMA crossover for additional confirmation
    bool ema_bullish = ma_fast_vd[0] > ma_slow_vd[0];
    bool ema_bearish = ma_fast_vd[0] < ma_slow_vd[0];
    bool ema_cross_up = (ma_fast_vd[0] > ma_slow_vd[0] && ma_fast_vd[1] <= ma_slow_vd[1]);
    bool ema_cross_down = (ma_fast_vd[0] < ma_slow_vd[0] && ma_fast_vd[1] >= ma_slow_vd[1]);

    // Volume confirmation logic
    bool volume_above_average = tick_volumes[0] > (tick_volumes[0] + tick_volumes[1] + tick_volumes[2]) / 3;

    // Update signals - Simplified for better execution
    volume_delta_bullish = (buy_percent >= InpVD_Threshold) && (ema_cross_up || ema_bullish) && volume_above_average;
    volume_delta_bearish = (sell_percent >= InpVD_Threshold) && (ema_cross_down || ema_bearish) && volume_above_average;

    if(InpLogSignals && InpEnableLogging)
    {
        if(volume_delta_bullish)
            Print("VOLUME DELTA: Bullish signal - Buy%: ", DoubleToString(buy_percent, 1),
                  " EMA Cross Up: ", ema_cross_up, " Vol Above Avg: ", volume_above_average);
        if(volume_delta_bearish)
            Print("VOLUME DELTA: Bearish signal - Sell%: ", DoubleToString(sell_percent, 1),
                  " EMA Cross Down: ", ema_cross_down, " Vol Above Avg: ", volume_above_average);
    }
}

//+------------------------------------------------------------------+
//| Update Trendlines                                               |
//+------------------------------------------------------------------+
void UpdateTrendlines()
{
    double high_prices[], low_prices[];
    ArraySetAsSeries(high_prices, true);
    ArraySetAsSeries(low_prices, true);

    int total_bars = InpTL_LeftBars + InpTL_RightBars + 1;

    if(CopyHigh(_Symbol, _Period, 0, total_bars + 10, high_prices) < total_bars + 10 ||
       CopyLow(_Symbol, _Period, 0, total_bars + 10, low_prices) < total_bars + 10)
        return;

    // Find pivot highs and lows
    bool pivot_high_found = false;
    bool pivot_low_found = false;
    double pivot_high_price = 0;
    double pivot_low_price = 0;
    int pivot_high_index = 0;
    int pivot_low_index = 0;

    // Check for pivot high
    int check_index = InpTL_RightBars;
    bool is_pivot_high = true;

    // Check left side
    for(int i = check_index + 1; i <= check_index + InpTL_LeftBars; i++)
    {
        if(high_prices[i] >= high_prices[check_index])
        {
            is_pivot_high = false;
            break;
        }
    }

    // Check right side
    if(is_pivot_high)
    {
        for(int i = 0; i < check_index; i++)
        {
            if(high_prices[i] >= high_prices[check_index])
            {
                is_pivot_high = false;
                break;
            }
        }
    }

    if(is_pivot_high)
    {
        pivot_high_found = true;
        pivot_high_price = high_prices[check_index];
        pivot_high_index = check_index;
    }

    // Check for pivot low
    bool is_pivot_low = true;

    // Check left side
    for(int i = check_index + 1; i <= check_index + InpTL_LeftBars; i++)
    {
        if(low_prices[i] <= low_prices[check_index])
        {
            is_pivot_low = false;
            break;
        }
    }

    // Check right side
    if(is_pivot_low)
    {
        for(int i = 0; i < check_index; i++)
        {
            if(low_prices[i] <= low_prices[check_index])
            {
                is_pivot_low = false;
                break;
            }
        }
    }

    if(is_pivot_low)
    {
        pivot_low_found = true;
        pivot_low_price = low_prices[check_index];
        pivot_low_index = check_index;
    }

    // Check proximity to trendlines
    double current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double pip_size = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    if(SymbolInfoInteger(_Symbol, SYMBOL_DIGITS) == 5 || SymbolInfoInteger(_Symbol, SYMBOL_DIGITS) == 3)
        pip_size *= 10;

    double proximity_distance = InpTL_ProximityPips * pip_size;

    // Update trendline signals
    trendline_support = pivot_low_found &&
                       MathAbs(current_price - pivot_low_price) <= proximity_distance &&
                       current_price > pivot_low_price;

    trendline_resistance = pivot_high_found &&
                          MathAbs(current_price - pivot_high_price) <= proximity_distance &&
                          current_price < pivot_high_price;

    if(InpLogSignals && InpEnableLogging)
    {
        if(trendline_support)
            Print("TRENDLINE: Support detected at ", DoubleToString(pivot_low_price, _Digits),
                  " Current: ", DoubleToString(current_price, _Digits));
        if(trendline_resistance)
            Print("TRENDLINE: Resistance detected at ", DoubleToString(pivot_high_price, _Digits),
                  " Current: ", DoubleToString(current_price, _Digits));
    }
}

//+------------------------------------------------------------------+
//| Check Entry Signals                                             |
//+------------------------------------------------------------------+
void CheckEntrySignals()
{
    // Check if we already have a position
    if(PositionSelect(_Symbol))
        return;

    // Count confirmed signals for more flexible entry
    int buy_confirmations = 0;
    int sell_confirmations = 0;

    if(supertrend_bullish) buy_confirmations++;
    if(macd_bullish) buy_confirmations++;
    if(volume_delta_bullish) buy_confirmations++;
    if(trendline_support) buy_confirmations++;

    if(supertrend_bearish) sell_confirmations++;
    if(macd_bearish) sell_confirmations++;
    if(volume_delta_bearish) sell_confirmations++;
    if(trendline_resistance) sell_confirmations++;

    // Require 3 out of 4 indicators instead of all 4 for better execution
    bool buy_signal = buy_confirmations >= 3;
    bool sell_signal = sell_confirmations >= 3;

    if(buy_signal)
    {
        if(InpLogSignals && InpEnableLogging)
            Print("=== BUY SIGNAL CONFIRMED ===");
            Print("Supertrend: ", supertrend_bullish);
            Print("MACD: ", macd_bullish);
            Print("Volume Delta: ", volume_delta_bullish);
            Print("Trendline Support: ", trendline_support);

        OpenBuyTrade();
    }
    else if(sell_signal)
    {
        if(InpLogSignals && InpEnableLogging)
            Print("=== SELL SIGNAL CONFIRMED ===");
            Print("Supertrend: ", supertrend_bearish);
            Print("MACD: ", macd_bearish);
            Print("Volume Delta: ", volume_delta_bearish);
            Print("Trendline Resistance: ", trendline_resistance);

        OpenSellTrade();
    }
}

//+------------------------------------------------------------------+
//| Open Buy Trade                                                  |
//+------------------------------------------------------------------+
void OpenBuyTrade()
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    // Calculate stop loss (using ATR or swing low)
    double stop_loss = CalculateStopLoss(ORDER_TYPE_BUY);
    if(stop_loss <= 0)
        return;

    // Calculate position size based on risk
    double lot_size = CalculatePositionSize(ask, stop_loss, true);
    if(lot_size <= 0)
        return;

    // Calculate take profit levels
    double sl_distance = ask - stop_loss;
    double tp1 = ask + (sl_distance * InpTP1_Ratio);
    double tp2 = ask + (sl_distance * InpTP2_Ratio);
    double tp3 = ask + (sl_distance * InpTP3_Ratio);

    // Open main position (will be split into 3 parts)
    if(trade.Buy(lot_size, _Symbol, ask, stop_loss, tp1, InpTradeComment))
    {
        ulong ticket = trade.ResultOrder();
        trades_today++;

        // Store position info for multi-level management
        tp_levels[0].ticket = ticket;
        tp_levels[0].volume = lot_size * InpTP1_Percent / 100;
        tp_levels[0].closed = false;

        tp_levels[1].ticket = ticket;
        tp_levels[1].volume = lot_size * InpTP2_Percent / 100;
        tp_levels[1].closed = false;

        tp_levels[2].ticket = ticket;
        tp_levels[2].volume = lot_size * InpTP3_Percent / 100;
        tp_levels[2].closed = false;

        if(InpLogTrades && InpEnableLogging)
        {
            Print("BUY ORDER OPENED:");
            Print("Ticket: ", ticket);
            Print("Volume: ", lot_size);
            Print("Entry: ", ask);
            Print("Stop Loss: ", stop_loss);
            Print("TP1: ", tp1, " (", InpTP1_Percent, "%)");
            Print("TP2: ", tp2, " (", InpTP2_Percent, "%)");
            Print("TP3: ", tp3, " (", InpTP3_Percent, "%)");
        }
    }
    else
    {
        Print("Failed to open BUY order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Open Sell Trade                                                 |
//+------------------------------------------------------------------+
void OpenSellTrade()
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    // Calculate stop loss (using ATR or swing high)
    double stop_loss = CalculateStopLoss(ORDER_TYPE_SELL);
    if(stop_loss <= 0)
        return;

    // Calculate position size based on risk
    double lot_size = CalculatePositionSize(bid, stop_loss, false);
    if(lot_size <= 0)
        return;

    // Calculate take profit levels
    double sl_distance = stop_loss - bid;
    double tp1 = bid - (sl_distance * InpTP1_Ratio);
    double tp2 = bid - (sl_distance * InpTP2_Ratio);
    double tp3 = bid - (sl_distance * InpTP3_Ratio);

    // Open main position (will be split into 3 parts)
    if(trade.Sell(lot_size, _Symbol, bid, stop_loss, tp1, InpTradeComment))
    {
        ulong ticket = trade.ResultOrder();
        trades_today++;

        // Store position info for multi-level management
        tp_levels[0].ticket = ticket;
        tp_levels[0].volume = lot_size * InpTP1_Percent / 100;
        tp_levels[0].closed = false;

        tp_levels[1].ticket = ticket;
        tp_levels[1].volume = lot_size * InpTP2_Percent / 100;
        tp_levels[1].closed = false;

        tp_levels[2].ticket = ticket;
        tp_levels[2].volume = lot_size * InpTP3_Percent / 100;
        tp_levels[2].closed = false;

        if(InpLogTrades && InpEnableLogging)
        {
            Print("SELL ORDER OPENED:");
            Print("Ticket: ", ticket);
            Print("Volume: ", lot_size);
            Print("Entry: ", bid);
            Print("Stop Loss: ", stop_loss);
            Print("TP1: ", tp1, " (", InpTP1_Percent, "%)");
            Print("TP2: ", tp2, " (", InpTP2_Percent, "%)");
            Print("TP3: ", tp3, " (", InpTP3_Percent, "%)");
        }
    }
    else
    {
        Print("Failed to open SELL order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Calculate Stop Loss                                             |
//+------------------------------------------------------------------+
double CalculateStopLoss(ENUM_ORDER_TYPE order_type)
{
    double high_prices[], low_prices[];
    ArraySetAsSeries(high_prices, true);
    ArraySetAsSeries(low_prices, true);

    if(CopyHigh(_Symbol, _Period, 0, 20, high_prices) < 20 ||
       CopyLow(_Symbol, _Period, 0, 20, low_prices) < 20)
        return 0;

    double stop_loss = 0;
    double current_price = (order_type == ORDER_TYPE_BUY) ?
                          SymbolInfoDouble(_Symbol, SYMBOL_ASK) :
                          SymbolInfoDouble(_Symbol, SYMBOL_BID);

    if(order_type == ORDER_TYPE_BUY)
    {
        // For BUY: Find recent swing low
        double swing_low = low_prices[0];
        for(int i = 1; i < 10; i++)
        {
            if(low_prices[i] < swing_low)
                swing_low = low_prices[i];
        }

        // Use ATR-based stop loss as alternative
        double atr_stop = current_price - (atr_buffer[0] * 1.5);

        // Use the closer of swing low or ATR stop (more conservative)
        stop_loss = MathMax(swing_low, atr_stop);

        // Ensure minimum distance
        double min_distance = SymbolInfoInteger(_Symbol, SYMBOL_SPREAD) * SymbolInfoDouble(_Symbol, SYMBOL_POINT) * 2;
        if(current_price - stop_loss < min_distance)
            stop_loss = current_price - min_distance;
    }
    else // SELL
    {
        // For SELL: Find recent swing high
        double swing_high = high_prices[0];
        for(int i = 1; i < 10; i++)
        {
            if(high_prices[i] > swing_high)
                swing_high = high_prices[i];
        }

        // Use ATR-based stop loss as alternative
        double atr_stop = current_price + (atr_buffer[0] * 1.5);

        // Use the closer of swing high or ATR stop (more conservative)
        stop_loss = MathMin(swing_high, atr_stop);

        // Ensure minimum distance
        double min_distance = SymbolInfoInteger(_Symbol, SYMBOL_SPREAD) * SymbolInfoDouble(_Symbol, SYMBOL_POINT) * 2;
        if(stop_loss - current_price < min_distance)
            stop_loss = current_price + min_distance;
    }

    return NormalizeDouble(stop_loss, _Digits);
}

//+------------------------------------------------------------------+
//| Calculate Position Size                                         |
//+------------------------------------------------------------------+
double CalculatePositionSize(double entry_price, double stop_loss, bool is_buy)
{
    double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = account_balance * InpRiskPercent / 100;

    // Calculate risk per pip
    double sl_distance = is_buy ? (entry_price - stop_loss) : (stop_loss - entry_price);
    if(sl_distance <= 0)
        return 0;

    // Get contract size and tick value
    double contract_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_CONTRACT_SIZE);
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

    // Calculate position size
    double position_size = (risk_amount * tick_size) / (sl_distance * tick_value);

    // Normalize to lot step
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);

    position_size = MathFloor(position_size / lot_step) * lot_step;
    position_size = MathMax(position_size, min_lot);
    position_size = MathMin(position_size, max_lot);

    // Additional safety check - ensure we don't risk more than intended
    double actual_risk = (sl_distance * position_size * tick_value) / tick_size;
    double risk_percent_check = (actual_risk / account_balance) * 100;

    if(risk_percent_check > InpRiskPercent * 1.1) // 10% tolerance
    {
        position_size = position_size * InpRiskPercent / risk_percent_check;
        position_size = MathFloor(position_size / lot_step) * lot_step;
        position_size = MathMax(position_size, min_lot);
    }

    if(InpEnableLogging)
    {
        Print("POSITION SIZING:");
        Print("Account Balance: ", account_balance);
        Print("Risk Amount: ", risk_amount, " (", InpRiskPercent, "%)");
        Print("SL Distance: ", sl_distance);
        Print("Position Size: ", position_size);
        Print("Actual Risk: ", actual_risk, " (", DoubleToString(risk_percent_check, 2), "%)");
    }

    return position_size;
}

//+------------------------------------------------------------------+
//| Manage Positions                                                |
//+------------------------------------------------------------------+
void ManagePositions()
{
    if(!PositionSelect(_Symbol))
        return;

    double current_price = PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ?
                          SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                          SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    double entry_price = PositionGetDouble(POSITION_PRICE_OPEN);
    double stop_loss = PositionGetDouble(POSITION_SL);
    double current_volume = PositionGetDouble(POSITION_VOLUME);
    bool is_buy = PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY;

    // Calculate profit in pips
    double pip_size = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    if(SymbolInfoInteger(_Symbol, SYMBOL_DIGITS) == 5 || SymbolInfoInteger(_Symbol, SYMBOL_DIGITS) == 3)
        pip_size *= 10;

    double profit_pips = is_buy ? (current_price - entry_price) / pip_size :
                                 (entry_price - current_price) / pip_size;

    // Calculate R (risk units)
    double sl_distance_pips = is_buy ? (entry_price - stop_loss) / pip_size :
                                      (stop_loss - entry_price) / pip_size;

    double r_multiple = (sl_distance_pips > 0) ? profit_pips / sl_distance_pips : 0;

    // Multi-level take profit management
    ManageMultiLevelTP(r_multiple, current_volume, is_buy);

    // Trailing stop after TP3
    if(r_multiple >= InpTP3_Ratio)
    {
        ImplementTrailingStop(is_buy, current_price, entry_price);
    }
}

//+------------------------------------------------------------------+
//| Manage Multi-Level Take Profit                                  |
//+------------------------------------------------------------------+
void ManageMultiLevelTP(double r_multiple, double current_volume, bool is_buy)
{
    // TP1 at 1R - Close 30%
    if(r_multiple >= InpTP1_Ratio && !tp_levels[0].closed)
    {
        double close_volume = current_volume * InpTP1_Percent / 100;
        if(close_volume >= SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN))
        {
            if(trade.PositionClosePartial(_Symbol, close_volume))
            {
                tp_levels[0].closed = true;
                if(InpLogTrades && InpEnableLogging)
                    Print("TP1 HIT: Closed ", InpTP1_Percent, "% at ", r_multiple, "R");
            }
        }
    }

    // TP2 at 2R - Close 40% and move SL to breakeven
    if(r_multiple >= InpTP2_Ratio && !tp_levels[1].closed)
    {
        double close_volume = current_volume * InpTP2_Percent / 100;
        if(close_volume >= SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN))
        {
            if(trade.PositionClosePartial(_Symbol, close_volume))
            {
                tp_levels[1].closed = true;

                // Move stop loss to breakeven
                double entry_price = PositionGetDouble(POSITION_PRICE_OPEN);
                trade.PositionModify(_Symbol, entry_price, PositionGetDouble(POSITION_TP));

                if(InpLogTrades && InpEnableLogging)
                    Print("TP2 HIT: Closed ", InpTP2_Percent, "% at ", r_multiple, "R, SL moved to breakeven");
            }
        }
    }

    // TP3 at 3R - Close remaining 30%
    if(r_multiple >= InpTP3_Ratio && !tp_levels[2].closed)
    {
        if(trade.PositionClose(_Symbol))
        {
            tp_levels[2].closed = true;

            // Reset TP levels for next trade
            for(int i = 0; i < 3; i++)
            {
                tp_levels[i].ticket = 0;
                tp_levels[i].volume = 0;
                tp_levels[i].closed = false;
            }

            if(InpLogTrades && InpEnableLogging)
                Print("TP3 HIT: Closed remaining position at ", r_multiple, "R");
        }
    }
}

//+------------------------------------------------------------------+
//| Implement Trailing Stop                                         |
//+------------------------------------------------------------------+
void ImplementTrailingStop(bool is_buy, double current_price, double entry_price)
{
    double current_sl = PositionGetDouble(POSITION_SL);
    double new_sl = current_sl;

    // Use ATR for trailing distance
    double trail_distance = atr_buffer[0] * 1.0; // 1 ATR trailing distance

    if(is_buy)
    {
        // For BUY positions, trail stop loss upward
        double potential_sl = current_price - trail_distance;

        // Only move SL up, never down
        if(potential_sl > current_sl)
        {
            new_sl = NormalizeDouble(potential_sl, _Digits);
        }
    }
    else
    {
        // For SELL positions, trail stop loss downward
        double potential_sl = current_price + trail_distance;

        // Only move SL down, never up
        if(potential_sl < current_sl || current_sl == 0)
        {
            new_sl = NormalizeDouble(potential_sl, _Digits);
        }
    }

    // Update stop loss if changed
    if(new_sl != current_sl)
    {
        double current_tp = PositionGetDouble(POSITION_TP);
        if(trade.PositionModify(_Symbol, new_sl, current_tp))
        {
            if(InpLogTrades && InpEnableLogging)
                Print("TRAILING STOP: Updated SL from ", current_sl, " to ", new_sl);
        }
    }
}

//+------------------------------------------------------------------+
//| Close All Positions                                             |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(position.SelectByIndex(i))
        {
            if(position.Symbol() == _Symbol && position.Magic() == InpMagicNumber)
            {
                trade.PositionClose(position.Ticket());
                if(InpLogTrades && InpEnableLogging)
                    Print("Position closed due to Friday close or emergency stop");
            }
        }
    }

    // Reset TP levels
    for(int i = 0; i < 3; i++)
    {
        tp_levels[i].ticket = 0;
        tp_levels[i].volume = 0;
        tp_levels[i].closed = false;
    }
}

//+------------------------------------------------------------------+
//| OnTrade Event Handler                                           |
//+------------------------------------------------------------------+
void OnTrade()
{
    // Log trade events
    if(InpLogTrades && InpEnableLogging)
    {
        HistorySelect(TimeCurrent() - 86400, TimeCurrent()); // Last 24 hours

        for(int i = HistoryDealsTotal() - 1; i >= 0; i--)
        {
            ulong deal_ticket = HistoryDealGetTicket(i);
            if(deal_ticket > 0)
            {
                string symbol = HistoryDealGetString(deal_ticket, DEAL_SYMBOL);
                long magic = HistoryDealGetInteger(deal_ticket, DEAL_MAGIC);

                if(symbol == _Symbol && magic == InpMagicNumber)
                {
                    ENUM_DEAL_TYPE deal_type = (ENUM_DEAL_TYPE)HistoryDealGetInteger(deal_ticket, DEAL_TYPE);
                    double deal_volume = HistoryDealGetDouble(deal_ticket, DEAL_VOLUME);
                    double deal_price = HistoryDealGetDouble(deal_ticket, DEAL_PRICE);
                    double deal_profit = HistoryDealGetDouble(deal_ticket, DEAL_PROFIT);

                    Print("TRADE EVENT - Type: ", EnumToString(deal_type),
                          " Volume: ", deal_volume,
                          " Price: ", deal_price,
                          " Profit: ", deal_profit);
                    break; // Only log the most recent deal
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Performance Monitoring                                          |
//+------------------------------------------------------------------+
void MonitorPerformance()
{
    static datetime last_report = 0;
    datetime current_time = TimeCurrent();

    // Report every hour
    if(current_time - last_report >= 3600)
    {
        last_report = current_time;

        double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
        double current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
        double daily_pnl = current_balance - daily_start_balance;
        double total_pnl = current_balance - total_start_balance;

        double daily_pnl_percent = (daily_start_balance > 0) ? (daily_pnl / daily_start_balance) * 100 : 0;
        double total_pnl_percent = (total_start_balance > 0) ? (total_pnl / total_start_balance) * 100 : 0;

        if(InpEnableLogging)
        {
            Print("=== PERFORMANCE REPORT ===");
            Print("Current Balance: ", current_balance);
            Print("Current Equity: ", current_equity);
            Print("Daily P&L: ", daily_pnl, " (", DoubleToString(daily_pnl_percent, 2), "%)");
            Print("Total P&L: ", total_pnl, " (", DoubleToString(total_pnl_percent, 2), "%)");
            Print("Trades Today: ", trades_today);
            Print("Trading Allowed: ", trading_allowed);
            Print("========================");
        }

        // Emergency stop if approaching limits
        if(daily_pnl_percent <= -2.5) // 2.5% daily loss warning
        {
            Print("WARNING: Approaching daily drawdown limit!");
        }

        if(total_pnl_percent <= -4.5) // 4.5% total loss warning
        {
            Print("WARNING: Approaching total drawdown limit!");
        }
    }
}

//+------------------------------------------------------------------+
//| Signal Strength Analysis                                        |
//+------------------------------------------------------------------+
int AnalyzeSignalStrength()
{
    int signal_count = 0;

    // Count confirmed signals
    if(supertrend_bullish || supertrend_bearish) signal_count++;
    if(macd_bullish || macd_bearish) signal_count++;
    if(volume_delta_bullish || volume_delta_bearish) signal_count++;
    if(trendline_support || trendline_resistance) signal_count++;

    return signal_count;
}

//+------------------------------------------------------------------+
//| Log Signal Status                                               |
//+------------------------------------------------------------------+
void LogSignalStatus()
{
    if(!InpLogSignals || !InpEnableLogging)
        return;

    static datetime last_signal_log = 0;
    datetime current_time = TimeCurrent();

    // Log signal status every 15 minutes
    if(current_time - last_signal_log >= 900)
    {
        last_signal_log = current_time;

        int signal_strength = AnalyzeSignalStrength();

        Print("=== SIGNAL STATUS ===");
        Print("Signal Strength: ", signal_strength, "/4");
        Print("Supertrend - Bull: ", supertrend_bullish, " Bear: ", supertrend_bearish);
        Print("MACD - Bull: ", macd_bullish, " Bear: ", macd_bearish);
        Print("Volume Delta - Bull: ", volume_delta_bullish, " Bear: ", volume_delta_bearish);
        Print("Trendline - Support: ", trendline_support, " Resistance: ", trendline_resistance);
        Print("Session Active: ", IsSessionActive());
        Print("Trading Allowed: ", trading_allowed);
        Print("====================");
    }
}

//+------------------------------------------------------------------+
//| Enhanced OnTick with Logging                                    |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check if new bar
    static datetime last_bar_time = 0;
    datetime current_bar_time = iTime(_Symbol, _Period, 0);

    if(current_bar_time == last_bar_time)
        return;

    last_bar_time = current_bar_time;

    // Update daily tracking
    UpdateDailyTracking();

    // Check risk management
    if(!CheckRiskManagement())
        return;

    // Check session filtering
    if(!IsSessionActive())
        return;

    // Update indicators
    UpdateIndicators();

    // Log signal status periodically
    LogSignalStatus();

    // Check for entry signals
    CheckEntrySignals();

    // Manage existing positions
    ManagePositions();

    // Monitor performance
    MonitorPerformance();
}

//+------------------------------------------------------------------+
//| Utility Functions                                               |
//+------------------------------------------------------------------+
string GetTimeString()
{
    return TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES);
}

string GetPositionSummary()
{
    if(!PositionSelect(_Symbol))
        return "No Position";

    double profit = PositionGetDouble(POSITION_PROFIT);
    double volume = PositionGetDouble(POSITION_VOLUME);
    string type = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? "BUY" : "SELL";

    return StringFormat("%s %.2f lots, P&L: %.2f", type, volume, profit);
}

//+------------------------------------------------------------------+
//| Expert Advisor Information                                       |
//+------------------------------------------------------------------+
void PrintEAInfo()
{
    Print("========================================");
    Print("Multi-Indicator Prop Firm EA v1.0");
    Print("Strategy: Supertrend + Zero Lag MACD + Volume Delta + Trendlines");
    Print("Risk Management: ", InpRiskPercent, "% per trade");
    Print("Max Daily DD: ", InpMaxDailyDD, "%");
    Print("Max Total DD: ", InpMaxTotalDD, "%");
    Print("Max Trades/Day: ", InpMaxTradesPerDay);
    Print("Symbol: ", _Symbol);
    Print("Timeframe: ", EnumToString(_Period));
    Print("========================================");
}

//+------------------------------------------------------------------+
//| Enhanced Initialization                                          |
//+------------------------------------------------------------------+
int OnInit()
{
    // Print EA information
    PrintEAInfo();

    // Set magic number
    trade.SetExpertMagicNumber(InpMagicNumber);

    // Initialize indicator handles
    handle_atr = iATR(_Symbol, _Period, InpSTR_Period);
    handle_ma_fast_vd = iMA(_Symbol, _Period, InpVD_EMAFast, 0, MODE_EMA, PRICE_CLOSE);
    handle_ma_slow_vd = iMA(_Symbol, _Period, InpVD_EMASlow, 0, MODE_EMA, PRICE_CLOSE);

    // Check handles
    if(handle_atr == INVALID_HANDLE || handle_ma_fast_vd == INVALID_HANDLE || handle_ma_slow_vd == INVALID_HANDLE)
    {
        Print("Error creating indicator handles");
        return INIT_FAILED;
    }

    // Initialize arrays
    ArraySetAsSeries(atr_buffer, true);
    ArraySetAsSeries(ma_fast_vd, true);
    ArraySetAsSeries(ma_slow_vd, true);

    // Initialize trading variables
    daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    total_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    trades_today = 0;
    last_trade_date = 0;
    trading_allowed = true;

    // Initialize position levels
    for(int i = 0; i < 3; i++)
    {
        tp_levels[i].ticket = 0;
        tp_levels[i].volume = 0;
        tp_levels[i].closed = false;
    }

    // Validate input parameters
    if(InpRiskPercent <= 0 || InpRiskPercent > 2.0)
    {
        Print("Invalid risk percentage. Must be between 0.1% and 2.0%");
        return INIT_PARAMETERS_INCORRECT;
    }

    if(InpMaxDailyDD <= 0 || InpMaxDailyDD > 10.0)
    {
        Print("Invalid daily drawdown limit. Must be between 0.1% and 10.0%");
        return INIT_PARAMETERS_INCORRECT;
    }

    // Log initialization
    if(InpEnableLogging)
        Print("MultiIndicator EA initialized successfully on ", _Symbol, " ", EnumToString(_Period));

    return INIT_SUCCEEDED;
}
