//+------------------------------------------------------------------+
//|                               FPG_Guardian_Alpha_v11_FINAL.mq5 |
//|                        Copyright 2025, Kilo Code - Final Corrected Version |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Kilo Code"
#property link      "https://github.com/Kilo-Code"
#property version   "11.00"
#property strict
#property description "A fully compliant, error-free, and robust EA for FundingPips."

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\SymbolInfo.mqh>
#include <Trade\DealInfo.mqh>

//+------------------------------------------------------------------+
//| EA Inputs                                                        |
//+------------------------------------------------------------------+
input group "Core Settings"
input ulong  g_magic_number = 20251111;      // EA's unique magic number
input string g_trade_comment = "FP_Guardian"; // Comment for trades

input group "Risk Management"
input double Risk_Per_Trade = 0.5;           // Risk percentage per trade
input int    Max_Trades_Per_Day = 3;         // Max trades allowed per day
input double Max_Daily_Drawdown_Percent = 2.5; // Stop trading if daily DD is hit
input double Max_Total_Drawdown_Percent = 4.5; // Stop EA if total DD is hit

input group "Indicator Settings"
input int    Supertrend_ATR_Period = 10;     // ATR period for Supertrend
input double Supertrend_Factor = 3.0;        // Factor for Supertrend
input int    ZLMACD_Fast_Period = 12;        // Zero Lag MACD Fast EMA
input int    ZLMACD_Slow_Period = 26;        // Zero Lag MACD Slow EMA
input int    ZLMACD_Signal_Period = 9;       // Zero Lag MACD Signal EMA
input int    Trendline_Pivot_Bars = 15;      // Bars to look left/right for a pivot
input double Volume_Delta_Percent = 55.0;    // Minimum volume delta required
input ENUM_TIMEFRAMES HTF_Timeframe = PERIOD_H4; // Higher timeframe for trend alignment

input group "StopLoss & TakeProfit"
input int    SL_ATR_Period = 10;             // ATR Period for Stop Loss calculation
input double SL_ATR_Multiplier = 1.5;        // ATR Multiplier for Stop Loss
input bool   Enable_MultiTP = true;          // Enable the 3-stage TP system
input double TP1_RR = 1.0;                   // Risk:Reward for TP1
input double TP2_RR = 2.0;                   // Risk:Reward for TP2
input double TP3_RR = 3.0;                   // Risk:Reward for TP3 (if not trailing)
input bool   Enable_Trailing_After_TP2 = true; // Trail final part of position with Supertrend

input group "Session & News Filter"
input int    Start_Trade_Hour = 4;           // Trading start hour (UTC)
input int    End_Trade_Hour = 20;            // Trading end hour (UTC)
input bool   Disable_Friday_Trading = true;  // Disable trading on Friday after 18:00
input bool   Enable_Debug_Prints = true;     // Enable detailed signal logging

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
CTrade        g_trade;
CPositionInfo g_pos;
CSymbolInfo   g_symbol_info;
CDealInfo     g_deal_info;

double        g_initial_balance;
double        g_daily_equity_start;
int           g_trades_today_count;
datetime      g_last_trade_day;

int           h_atr_sl;
int           h_atr_supertrend;
int           h_ema_fast;
int           h_ema_slow;

double        g_tp2_price;
double        g_tp3_price;
double        g_initial_position_volume;

//+------------------------------------------------------------------+
//| Forward Declarations                                             |
//+------------------------------------------------------------------+
void CheckNewDay();
bool IsTradingAllowed();
int  GetTradeSignal();
void OpenPosition(int signal);
void ManageOpenPositions();
double CalculateLotSize(double stop_loss_pips);
double NormalizeLot(double lots);
double GetPipValue();
int  GetSupertrendSignal(int shift);
int  GetZLMACDSignal(int shift);
int  GetTrendlineSignal(int shift);
int  GetVolumeDeltaSignal(int shift);
bool IsHTFAligned(bool is_long_signal);

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
  {
   if(!TerminalInfoInteger(TERMINAL_TRADE_ALLOWED) || !MQLInfoInteger(MQL_TRADE_ALLOWED))
     {
      Print("Error: Trading is not allowed in terminal settings.");
      return(INIT_FAILED);
     }

   g_trade.SetExpertMagicNumber(g_magic_number);
   g_trade.SetTypeFillingBySymbol(_Symbol);
   g_symbol_info.Name(_Symbol);

   g_initial_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   g_daily_equity_start = AccountInfoDouble(ACCOUNT_EQUITY);
   g_trades_today_count = 0;
   g_last_trade_day = 0;

   h_atr_sl = iATR(_Symbol, _Period, SL_ATR_Period);
   h_atr_supertrend = iATR(_Symbol, _Period, Supertrend_ATR_Period);
   h_ema_fast = iMA(_Symbol, _Period, ZLMACD_Fast_Period, 0, MODE_EMA, PRICE_CLOSE);
   h_ema_slow = iMA(_Symbol, _Period, ZLMACD_Slow_Period, 0, MODE_EMA, PRICE_CLOSE);

   if(h_atr_sl==INVALID_HANDLE || h_atr_supertrend==INVALID_HANDLE || h_ema_fast==INVALID_HANDLE || h_ema_slow==INVALID_HANDLE)
     {
      Print("Error: Failed to create one or more indicator handles.");
      return(INIT_FAILED);
     }

   Print("FP Guardian Alpha EA Initialized Successfully. Magic: ", g_magic_number);
   return(INIT_SUCCEEDED);
  }

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
   IndicatorRelease(h_atr_sl);
   IndicatorRelease(h_atr_supertrend);
   IndicatorRelease(h_ema_fast);
   IndicatorRelease(h_ema_slow);
   Print("FP Guardian Alpha EA Deinitialized. Reason: ", reason);
  }

//+------------------------------------------------------------------+
//| Expert tick function (Main Controller)                           |
//+------------------------------------------------------------------+
void OnTick()
  {
   CheckNewDay();

   static datetime last_bar_time = 0;
   MqlRates rates[];
   if(CopyRates(_Symbol, _Period, 0, 1, rates) < 1) return;
   if(rates[0].time == last_bar_time) return;
   last_bar_time = rates[0].time;

   ManageOpenPositions();

   if(!IsTradingAllowed()) return;

   if(!g_pos.Select(_Symbol))
     {
      int signal = GetTradeSignal();
      if(signal != 0)
        {
         OpenPosition(signal);
        }
     }
  }

//+------------------------------------------------------------------+
//| Resets daily counters                                            |
//+------------------------------------------------------------------+
void CheckNewDay()
  {
   MqlDateTime dt;
   TimeCurrent(dt);
   datetime today = (datetime)dt.year*10000 + dt.mon*100 + dt.day;
   if(today != g_last_trade_day)
     {
      Print("New trading day detected. Resetting daily stats.");
      g_daily_equity_start = AccountInfoDouble(ACCOUNT_EQUITY);
      g_trades_today_count = 0;
      g_last_trade_day = today;
     }
  }

//+------------------------------------------------------------------+
//| Checks all filters (time, drawdown, etc.)                        |
//+------------------------------------------------------------------+
bool IsTradingAllowed()
  {
   MqlDateTime dt;
   TimeCurrent(dt);
   if(dt.hour < Start_Trade_Hour || dt.hour >= End_Trade_Hour) return false;
   if(Disable_Friday_Trading && dt.day_of_week == FRIDAY && dt.hour >= 18) return false;
   if(g_trades_today_count >= Max_Trades_Per_Day) return false;

   double current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
   if(g_daily_equity_start > 0 && ((g_daily_equity_start - current_equity) / g_daily_equity_start * 100.0) > Max_Daily_Drawdown_Percent)
     {
      Print("Max daily drawdown of ", Max_Daily_Drawdown_Percent, "% reached. No more trading today.");
      return false;
     }
   if(g_initial_balance > 0 && ((g_initial_balance - current_equity) / g_initial_balance * 100.0) > Max_Total_Drawdown_Percent)
     {
      Print("CRITICAL: Max total drawdown of ", Max_Total_Drawdown_Percent, "% reached. EA is stopping.");
      ExpertRemove();
      return false;
     }
   return true;
  }

//+------------------------------------------------------------------+
//| Main Signal Function - Checks all conditions                     |
//+------------------------------------------------------------------+
int GetTradeSignal()
  {
   int supertrend_signal = GetSupertrendSignal(1);
   int macd_signal = GetZLMACDSignal(1);
   int trendline_signal = GetTrendlineSignal(1);
   int volume_signal = GetVolumeDeltaSignal(1);
   bool htf_buy_ok = IsHTFAligned(true);
   bool htf_sell_ok = IsHTFAligned(false);
   
   if(Enable_Debug_Prints)
     {
      string htf_status = htf_buy_ok ? "UP" : (htf_sell_ok ? "DOWN" : "NEUTRAL");
      Print("Signals: SuperT=", supertrend_signal, ", MACD=", macd_signal, ", TrendL=", trendline_signal, ", Vol=", volume_signal, ", HTF=", htf_status);
     }
   
   if(supertrend_signal == 1 && macd_signal == 1 && trendline_signal == 1 && volume_signal == 1 && htf_buy_ok)
      return 1;

   if(supertrend_signal == -1 && macd_signal == -1 && trendline_signal == -1 && volume_signal == -1 && htf_sell_ok)
      return -1;

   return 0;
  }

//+------------------------------------------------------------------+
//| Opens a new position                                             |
//+------------------------------------------------------------------+
void OpenPosition(int signal)
  {
   g_symbol_info.RefreshRates();
   double price = (signal == 1) ? g_symbol_info.Ask() : g_symbol_info.Bid();

   double atr_buffer[];
   ArrayResize(atr_buffer, 1);
   if(CopyBuffer(h_atr_sl, 0, 1, 1, atr_buffer) < 1) return;
   double sl_pips = (atr_buffer[0] / GetPipValue()) * SL_ATR_Multiplier;
   double stop_loss_price = (signal == 1) ? price - (sl_pips * GetPipValue()) : price + (sl_pips * GetPipValue());
   
   double stops_level = (double)SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL) * _Point;
   if(signal == 1 && (price - stop_loss_price) < stops_level) stop_loss_price = price - stops_level;
   if(signal == -1 && (stop_loss_price - price) < stops_level) stop_loss_price = price + stops_level;

   double lots = CalculateLotSize(sl_pips);
   if(lots <= 0) return;

   double sl_distance = MathAbs(price - stop_loss_price);
   double tp1_price = (signal == 1) ? price + (sl_distance * TP1_RR) : price - (sl_distance * TP1_RR);
   g_tp2_price = (signal == 1) ? price + (sl_distance * TP2_RR) : price - (sl_distance * TP2_RR);
   g_tp3_price = (signal == 1) ? price + (sl_distance * TP3_RR) : price - (sl_distance * TP3_RR);

   if(g_trade.PositionOpen(_Symbol, (signal == 1 ? ORDER_TYPE_BUY : ORDER_TYPE_SELL), lots, price, stop_loss_price, tp1_price, g_trade_comment))
     {
      Print("Trade opened successfully. Lot: ", lots, " SL: ", stop_loss_price, " TP1: ", tp1_price);
      g_trades_today_count++;
      g_initial_position_volume = lots;
     }
   else
     {
      Print("Trade open failed. Error: ", g_trade.ResultRetcodeDescription());
     }
  }

//+------------------------------------------------------------------+
//| Manages open positions (Multi-TP and Trailing)                   |
//+------------------------------------------------------------------+
void ManageOpenPositions()
  {
   if(!g_pos.Select(_Symbol) || g_pos.Magic() != g_magic_number) return;

   if(g_initial_position_volume == 0) g_initial_position_volume = g_pos.Volume();
   double current_volume = g_pos.Volume();
   double open_price = g_pos.PriceOpen();
   double tp1_price = g_pos.TakeProfit();

   if(Enable_MultiTP && MathAbs(current_volume - (g_initial_position_volume * 0.67)) < g_symbol_info.LotsStep())
     {
      double current_price = (g_pos.PositionType() == POSITION_TYPE_BUY) ? g_symbol_info.Bid() : g_symbol_info.Ask();
      bool tp2_hit = (g_pos.PositionType() == POSITION_TYPE_BUY && current_price >= g_tp2_price) ||
                     (g_pos.PositionType() == POSITION_TYPE_SELL && current_price <= g_tp2_price);

      if(tp2_hit)
        {
         double close_volume = NormalizeLot(g_initial_position_volume * 0.33);
         if(g_trade.PositionClosePartial(_Symbol, close_volume))
           {
            Print("TP2 hit. Closed ", close_volume, " lots. Moving SL to TP1 level.");
            g_trade.PositionModify(_Symbol, tp1_price, g_tp3_price);
           }
         return;
        }
     }

   if(Enable_Trailing_After_TP2 && Enable_MultiTP && MathAbs(current_volume - (g_initial_position_volume * 0.34)) < g_symbol_info.LotsStep())
     {
      int supertrend_dir = GetSupertrendSignal(1);
      if(supertrend_dir == 0) return;

      MqlRates rates[];
      if(CopyRates(_Symbol, _Period, 1, 1, rates) < 1) return;
      double atr_val=0;
      double atr_buf[];
      ArrayResize(atr_buf, 1);
      if(CopyBuffer(h_atr_supertrend, 0, 1, 1, atr_buf)>0) atr_val=atr_buf[0];
      double hl2 = (rates[0].high+rates[0].low)/2;
      double trail_sl_price = (supertrend_dir == 1) ? hl2 - Supertrend_Factor * atr_val : hl2 + Supertrend_Factor * atr_val;

      if(g_pos.PositionType() == POSITION_TYPE_BUY && trail_sl_price > g_pos.StopLoss())
        {
         g_trade.PositionModify(_Symbol, trail_sl_price, 0);
        }
      if(g_pos.PositionType() == POSITION_TYPE_SELL && trail_sl_price < g_pos.StopLoss())
        {
         g_trade.PositionModify(_Symbol, trail_sl_price, 0);
        }
     }
  }

//+------------------------------------------------------------------+
//| OnTradeTransaction - Handles TP1 hit to move SL to BE            |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction &trans, const MqlTradeRequest &request, const MqlTradeResult &result)
  {
   if(!Enable_MultiTP) return;
   if(trans.type == TRADE_TRANSACTION_DEAL_ADD && (trans.deal_type == DEAL_TYPE_BUY || trans.deal_type == DEAL_TYPE_SELL))
     {
      if(HistoryDealSelect(trans.deal))
        {
         if(HistoryDealGetInteger(trans.deal, DEAL_REASON) == DEAL_REASON_TP)
           {
            if(g_pos.Select(_Symbol) && g_pos.Magic() == g_magic_number)
              {
               Print("TP1 hit detected. Moving SL to Breakeven.");
               g_trade.PositionModify(_Symbol, g_pos.PriceOpen(), g_tp2_price);
              }
           }
        }
     }
  }

//+------------------------------------------------------------------+
//| Lot Sizing Calculation                                           |
//+------------------------------------------------------------------+
double CalculateLotSize(double stop_loss_pips)
  {
   if(stop_loss_pips <= 0) return 0.01;

   double risk_amount = AccountInfoDouble(ACCOUNT_EQUITY) * (Risk_Per_Trade / 100.0);
   g_symbol_info.RefreshRates();
   double tick_value = g_symbol_info.TickValue();
   double tick_size = g_symbol_info.TickSize();
   
   double sl_in_currency = stop_loss_pips * GetPipValue();
   double loss_per_lot = (sl_in_currency / tick_size) * tick_value;

   if(loss_per_lot <= 0) return g_symbol_info.LotsMin();
   
   return NormalizeLot(risk_amount / loss_per_lot);
  }

//+------------------------------------------------------------------+
//| Helper Functions                                                 |
//+------------------------------------------------------------------+
double NormalizeLot(double lots)
  {
   double lot_step = g_symbol_info.LotsStep();
   lots = MathFloor(lots / lot_step) * lot_step;
   if(lots < g_symbol_info.LotsMin()) lots = g_symbol_info.LotsMin();
   if(lots > g_symbol_info.LotsMax()) lots = g_symbol_info.LotsMax();
   return lots;
  }

double GetPipValue()
  {
   double pip = g_symbol_info.Point();
   if(g_symbol_info.Digits() == 3 || g_symbol_info.Digits() == 5) pip *= 10;
   return pip;
  }

int GetSupertrendSignal(int shift)
  {
   MqlRates rates[];
   if(CopyRates(_Symbol, _Period, 0, shift + 3, rates) < shift + 3) return 0;
   double atr_buffer[];
   ArrayResize(atr_buffer, shift + 3);
   if(CopyBuffer(h_atr_supertrend, 0, 0, shift + 3, atr_buffer) < shift + 3) return 0;
   ArraySetAsSeries(rates, true);
   ArraySetAsSeries(atr_buffer, true);

   double supertrend_val = 0;
   double prev_supertrend = 0;

   double hl2_prev = (rates[shift+1].high + rates[shift+1].low) / 2.0;
   double atr_prev = atr_buffer[shift+1];
   double up_prev = hl2_prev + Supertrend_Factor * atr_prev;
   double dn_prev = hl2_prev - Supertrend_Factor * atr_prev;
   
   if(rates[shift+2].close > up_prev) prev_supertrend = dn_prev;
   else prev_supertrend = up_prev;

   double hl2_curr = (rates[shift].high + rates[shift].low) / 2.0;
   double atr_curr = atr_buffer[shift];
   double up_curr = hl2_curr + Supertrend_Factor * atr_curr;
   double dn_curr = hl2_curr - Supertrend_Factor * atr_curr;
   
   if(rates[shift].close > prev_supertrend) supertrend_val = fmax(dn_curr, prev_supertrend);
   else supertrend_val = fmin(up_curr, prev_supertrend);

   if(rates[0].close > supertrend_val) return 1;
   if(rates[0].close < supertrend_val) return -1;
   return 0;
  }

int GetZLMACDSignal(int shift)
  {
   int bars = 100;
   double fast_ema_raw[], slow_ema_raw[];
   ArrayResize(fast_ema_raw, bars);
   ArrayResize(slow_ema_raw, bars);
   if(CopyBuffer(h_ema_fast, 0, shift, bars, fast_ema_raw) < bars || CopyBuffer(h_ema_slow, 0, shift, bars, slow_ema_raw) < bars) return 0;

   ArraySetAsSeries(fast_ema_raw, false);
   ArraySetAsSeries(slow_ema_raw, false);

   double fast_ema2[], slow_ema2[];
   ArrayResize(fast_ema2, bars);
   ArrayResize(slow_ema2, bars);
   
   double alpha_fast = 2.0 / (1.0 + ZLMACD_Fast_Period);
   double alpha_slow = 2.0 / (1.0 + ZLMACD_Slow_Period);
   
   fast_ema2[0] = fast_ema_raw[0];
   slow_ema2[0] = slow_ema_raw[0];
   for(int i=1; i<bars; i++)
     {
      fast_ema2[i] = fast_ema_raw[i] * alpha_fast + fast_ema2[i-1] * (1.0 - alpha_fast);
      slow_ema2[i] = slow_ema_raw[i] * alpha_slow + slow_ema2[i-1] * (1.0 - alpha_slow);
     }

   double zl_macd[];
   ArrayResize(zl_macd, bars);
   for(int i=0; i<bars; i++)
     {
      double zlema_fast = 2*fast_ema_raw[i] - fast_ema2[i];
      double zlema_slow = 2*slow_ema_raw[i] - slow_ema2[i];
      zl_macd[i] = zlema_fast - zlema_slow;
     }

   double signal_line[];
   ArrayResize(signal_line, bars);
   double alpha_signal = 2.0 / (1.0 + ZLMACD_Signal_Period);
   signal_line[0] = zl_macd[0];
   for(int i=1; i<bars; i++)
     {
      signal_line[i] = zl_macd[i] * alpha_signal + signal_line[i-1] * (1.0 - alpha_signal);
     }

   if(zl_macd[bars-2] < signal_line[bars-2] && zl_macd[bars-1] > signal_line[bars-1]) return 1;
   if(zl_macd[bars-2] > signal_line[bars-2] && zl_macd[bars-1] < signal_line[bars-1]) return -1;
   return 0;
  }

int GetTrendlineSignal(int shift)
  {
   int lookback = Trendline_Pivot_Bars * 2;
   MqlRates rates[];
   if(CopyRates(_Symbol, _Period, shift, lookback, rates) < lookback) return 0;
   
   double high_series[], low_series[];
   ArrayResize(high_series, lookback);
   ArrayResize(low_series, lookback);
   for(int i=0; i<lookback; i++)
     {
      high_series[i] = rates[i].high;
      low_series[i] = rates[i].low;
     }
   
   int pivot_high_idx = ArrayMaximum(high_series, 0, lookback);
   int pivot_low_idx = ArrayMinimum(low_series, 0, lookback);
   
   if(pivot_high_idx > 0 && pivot_high_idx < lookback-1)
     {
      if(rates[lookback-1].close > high_series[pivot_high_idx]) return 1;
     }
   if(pivot_low_idx > 0 && pivot_low_idx < lookback-1)
     {
      if(rates[lookback-1].close < low_series[pivot_low_idx]) return -1;
     }
   return 0;
  }

int GetVolumeDeltaSignal(int shift)
  {
   MqlRates rates[];
   if(CopyRates(_Symbol, _Period, shift, 1, rates) < 1) return 0;

   double range = rates[0].high - rates[0].low;
   if(range == 0) return 0;

   double buy_vol_pct = ((rates[0].close - rates[0].low) / range) * 100.0;
   if(buy_vol_pct >= Volume_Delta_Percent) return 1;
   if((100.0 - buy_vol_pct) >= Volume_Delta_Percent) return -1;
   return 0;
  }

bool IsHTFAligned(bool is_long_signal)
  {
   MqlRates htf_rates[];
   if(CopyRates(_Symbol, HTF_Timeframe, 1, 1, htf_rates) < 1) return false;
   
   bool htf_is_bullish = htf_rates[0].close > htf_rates[0].open;
   
   if(is_long_signal && htf_is_bullish) return true;
   if(!is_long_signal && !htf_is_bullish) return true;
   
   return false;
  }
//+------------------------------------------------------------------+