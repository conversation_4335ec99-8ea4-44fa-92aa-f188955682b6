//+------------------------------------------------------------------+
//|                                       FirstCandle_Breakout_EA.mq5 |
//|                                  Copyright 2024, Augment Agent    |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Augment Agent"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "First 15min Candle High/Low Breakout Strategy on 5min TF"
#property description "Marks first 15min candle levels, trades 5min breakouts with retest"

//--- Include necessary libraries
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>

//--- Global objects
CTrade         trade;
CPositionInfo  position;

//+------------------------------------------------------------------+
//| Input Parameters                                                 |
//+------------------------------------------------------------------+
input group "=== STRATEGY SETTINGS ==="
input bool     InpEnableTrading = true;                    // Enable Trading
input int      InpMagicNumber = 456789;                    // Magic Number
input string   InpTradeComment = "FirstCandle_EA";         // Trade Comment

input group "=== FIRST CANDLE SETTINGS ==="
input int      InpDayStartHour = 0;                        // Day Start Hour (0 = Midnight)
input int      InpDayStartMinute = 0;                      // Day Start Minute
input double   InpBreakoutPips = 3.0;                      // Breakout Confirmation (Pips)
input double   InpRetestPips = 8.0;                        // Retest Distance (Pips)
input int      InpRetestBars = 10;                         // Max Bars to Wait for Retest

input group "=== MONEY MANAGEMENT ==="
input double   InpRiskPercent = 1.0;                       // Risk Per Trade (%)
input double   InpStopLossPips = 40.0;                     // Stop Loss (Pips)
input double   InpTakeProfitPips = 80.0;                   // Take Profit (Pips)
input double   InpMaxDailyLoss = 3.0;                      // Max Daily Loss (%)
input int      InpMaxTradesPerDay = 8;                     // Max Trades Per Day

input group "=== TRADE MANAGEMENT ==="
input bool     InpUseTrailingStop = true;                  // Use Trailing Stop
input double   InpTrailingStopPips = 25.0;                 // Trailing Stop Distance (Pips)
input double   InpTrailingStepPips = 8.0;                  // Trailing Step (Pips)
input bool     InpMoveToBreakeven = true;                  // Move SL to Breakeven
input double   InpBreakevenPips = 20.0;                    // Breakeven Trigger (Pips)

input group "=== LOGGING ==="
input bool     InpEnableLogging = true;                    // Enable Logging
input bool     InpShowLevels = true;                       // Show Levels on Chart

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
// First candle levels
double first_candle_high;
double first_candle_low;
datetime first_candle_time;
bool levels_set;

// Breakout tracking
bool high_broken;
bool low_broken;
bool waiting_for_retest_high;
bool waiting_for_retest_low;
datetime breakout_time_high;
datetime breakout_time_low;
int retest_bars_high;
int retest_bars_low;

// Money management
double daily_start_balance;
int trades_today;
datetime last_trade_date;

// Price levels
double pip_size;
double breakout_distance;
double retest_distance;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Check if we're on 5-minute timeframe
    if(_Period != PERIOD_M5)
    {
        Print("ERROR: This EA must be run on M5 timeframe!");
        return INIT_FAILED;
    }
    
    // Set magic number
    trade.SetExpertMagicNumber(InpMagicNumber);
    
    // Calculate pip size
    pip_size = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    if(SymbolInfoInteger(_Symbol, SYMBOL_DIGITS) == 5 || SymbolInfoInteger(_Symbol, SYMBOL_DIGITS) == 3)
        pip_size *= 10;
    
    breakout_distance = InpBreakoutPips * pip_size;
    retest_distance = InpRetestPips * pip_size;
    
    // Initialize variables
    first_candle_high = 0;
    first_candle_low = 0;
    first_candle_time = 0;
    levels_set = false;
    
    high_broken = false;
    low_broken = false;
    waiting_for_retest_high = false;
    waiting_for_retest_low = false;
    breakout_time_high = 0;
    breakout_time_low = 0;
    retest_bars_high = 0;
    retest_bars_low = 0;
    
    daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    trades_today = 0;
    last_trade_date = 0;
    
    if(InpEnableLogging)
        Print("First Candle Breakout EA initialized on ", _Symbol, " M5");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Clean up chart objects
    if(InpShowLevels)
    {
        ObjectDelete(0, "FirstCandleHigh");
        ObjectDelete(0, "FirstCandleLow");
        ObjectDelete(0, "FirstCandleBox");
    }
    
    if(InpEnableLogging)
        Print("First Candle Breakout EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check if new bar
    static datetime last_bar_time = 0;
    datetime current_bar_time = iTime(_Symbol, _Period, 0);
    
    if(current_bar_time == last_bar_time)
        return;
    
    last_bar_time = current_bar_time;
    
    // Update daily tracking
    UpdateDailyTracking();
    
    // Check money management
    if(!CheckMoneyManagement())
        return;
    
    // Set first candle levels if needed
    SetFirstCandleLevels();
    
    // Check for breakouts on 5min timeframe
    CheckBreakouts();
    
    // Check for retests and entries
    CheckRetestEntries();
    
    // Manage existing positions
    ManagePositions();
}

//+------------------------------------------------------------------+
//| Update Daily Tracking                                           |
//+------------------------------------------------------------------+
void UpdateDailyTracking()
{
    datetime current_date = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
    
    if(last_trade_date != current_date)
    {
        // New day - reset counters and levels
        trades_today = 0;
        daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
        last_trade_date = current_date;
        
        // Reset first candle levels for new day
        levels_set = false;
        high_broken = false;
        low_broken = false;
        waiting_for_retest_high = false;
        waiting_for_retest_low = false;
        retest_bars_high = 0;
        retest_bars_low = 0;
        
        if(InpEnableLogging)
            Print("New trading day started. Balance: ", daily_start_balance);
    }
}

//+------------------------------------------------------------------+
//| Check Money Management                                           |
//+------------------------------------------------------------------+
bool CheckMoneyManagement()
{
    double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    
    // Check daily loss limit
    double daily_loss = (daily_start_balance - current_balance) / daily_start_balance * 100;
    if(daily_loss >= InpMaxDailyLoss)
    {
        if(InpEnableLogging)
            Print("Daily loss limit reached: ", daily_loss, "%");
        return false;
    }
    
    // Check max trades per day
    if(trades_today >= InpMaxTradesPerDay)
    {
        if(InpEnableLogging)
            Print("Maximum trades per day reached: ", trades_today);
        return false;
    }
    
    return InpEnableTrading;
}

//+------------------------------------------------------------------+
//| Set First Candle High/Low Levels from 15min TF                 |
//+------------------------------------------------------------------+
void SetFirstCandleLevels()
{
    if(levels_set)
        return;
    
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    
    // Check if we're past the first 15-minute candle of the day
    if(dt.hour > InpDayStartHour || (dt.hour == InpDayStartHour && dt.min >= InpDayStartMinute + 15))
    {
        // Calculate the time of the first 15-minute candle
        datetime day_start = StringToTime(TimeToString(TimeCurrent(), TIME_DATE)) + 
                            InpDayStartHour * 3600 + InpDayStartMinute * 60;
        
        // Get the first 15-minute candle data
        double high_15m = iHigh(_Symbol, PERIOD_M15, iBarShift(_Symbol, PERIOD_M15, day_start));
        double low_15m = iLow(_Symbol, PERIOD_M15, iBarShift(_Symbol, PERIOD_M15, day_start));
        datetime time_15m = iTime(_Symbol, PERIOD_M15, iBarShift(_Symbol, PERIOD_M15, day_start));
        
        if(high_15m > 0 && low_15m > 0)
        {
            first_candle_high = high_15m;
            first_candle_low = low_15m;
            first_candle_time = time_15m;
            levels_set = true;
            
            // Draw levels on chart
            if(InpShowLevels)
                DrawFirstCandleLevels();
            
            if(InpEnableLogging)
            {
                Print("First 15min candle levels set:");
                Print("High: ", DoubleToString(first_candle_high, _Digits));
                Print("Low: ", DoubleToString(first_candle_low, _Digits));
                Print("Time: ", TimeToString(first_candle_time));
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Draw First Candle Levels on Chart                               |
//+------------------------------------------------------------------+
void DrawFirstCandleLevels()
{
    // Remove existing lines
    ObjectDelete(0, "FirstCandleHigh");
    ObjectDelete(0, "FirstCandleLow");
    ObjectDelete(0, "FirstCandleBox");
    
    // Draw first candle high line
    ObjectCreate(0, "FirstCandleHigh", OBJ_HLINE, 0, 0, first_candle_high);
    ObjectSetInteger(0, "FirstCandleHigh", OBJPROP_COLOR, clrRed);
    ObjectSetInteger(0, "FirstCandleHigh", OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, "FirstCandleHigh", OBJPROP_WIDTH, 2);
    ObjectSetString(0, "FirstCandleHigh", OBJPROP_TEXT, "First Candle High: " + DoubleToString(first_candle_high, _Digits));
    
    // Draw first candle low line
    ObjectCreate(0, "FirstCandleLow", OBJ_HLINE, 0, 0, first_candle_low);
    ObjectSetInteger(0, "FirstCandleLow", OBJPROP_COLOR, clrBlue);
    ObjectSetInteger(0, "FirstCandleLow", OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, "FirstCandleLow", OBJPROP_WIDTH, 2);
    ObjectSetString(0, "FirstCandleLow", OBJPROP_TEXT, "First Candle Low: " + DoubleToString(first_candle_low, _Digits));
    
    // Draw rectangle to highlight the first candle range
    datetime end_time = first_candle_time + 15 * 60; // 15 minutes later
    ObjectCreate(0, "FirstCandleBox", OBJ_RECTANGLE, 0, first_candle_time, first_candle_high, end_time, first_candle_low);
    ObjectSetInteger(0, "FirstCandleBox", OBJPROP_COLOR, clrYellow);
    ObjectSetInteger(0, "FirstCandleBox", OBJPROP_STYLE, STYLE_DASH);
    ObjectSetInteger(0, "FirstCandleBox", OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, "FirstCandleBox", OBJPROP_FILL, false);
    ObjectSetInteger(0, "FirstCandleBox", OBJPROP_BACK, false);
}

//+------------------------------------------------------------------+
//| Check for Breakouts on 5min Timeframe                          |
//+------------------------------------------------------------------+
void CheckBreakouts()
{
    if(!levels_set)
        return;

    double current_high = iHigh(_Symbol, _Period, 0);
    double current_low = iLow(_Symbol, _Period, 0);
    double current_close = iClose(_Symbol, _Period, 0);

    // Check for high breakout
    if(!high_broken && current_high > first_candle_high + breakout_distance)
    {
        high_broken = true;
        waiting_for_retest_high = true;
        breakout_time_high = TimeCurrent();
        retest_bars_high = 0;

        if(InpEnableLogging)
            Print("First Candle HIGH broken at: ", DoubleToString(current_high, _Digits),
                  " Level was: ", DoubleToString(first_candle_high, _Digits));
    }

    // Check for low breakout
    if(!low_broken && current_low < first_candle_low - breakout_distance)
    {
        low_broken = true;
        waiting_for_retest_low = true;
        breakout_time_low = TimeCurrent();
        retest_bars_low = 0;

        if(InpEnableLogging)
            Print("First Candle LOW broken at: ", DoubleToString(current_low, _Digits),
                  " Level was: ", DoubleToString(first_candle_low, _Digits));
    }

    // Update retest bar counters
    if(waiting_for_retest_high)
    {
        retest_bars_high++;
        if(retest_bars_high > InpRetestBars)
        {
            waiting_for_retest_high = false;
            if(InpEnableLogging)
                Print("High retest timeout - no longer waiting");
        }
    }

    if(waiting_for_retest_low)
    {
        retest_bars_low++;
        if(retest_bars_low > InpRetestBars)
        {
            waiting_for_retest_low = false;
            if(InpEnableLogging)
                Print("Low retest timeout - no longer waiting");
        }
    }
}

//+------------------------------------------------------------------+
//| Check for Retest Entries                                        |
//+------------------------------------------------------------------+
void CheckRetestEntries()
{
    if(!levels_set)
        return;

    // Skip if we already have a position
    if(PositionSelect(_Symbol))
        return;

    double current_high = iHigh(_Symbol, _Period, 0);
    double current_low = iLow(_Symbol, _Period, 0);
    double current_close = iClose(_Symbol, _Period, 0);
    double prev_close = iClose(_Symbol, _Period, 1);

    // Check for retest of broken high (BUY setup)
    if(waiting_for_retest_high && high_broken)
    {
        // Price should come back to test the first candle high level
        bool retest_condition = (current_low <= first_candle_high + retest_distance &&
                                current_close > first_candle_high &&
                                prev_close <= first_candle_high);

        if(retest_condition)
        {
            // Retest confirmed - place BUY order
            OpenBuyTrade();
            waiting_for_retest_high = false;

            if(InpEnableLogging)
                Print("HIGH retest confirmed - BUY signal at: ", DoubleToString(current_close, _Digits));
        }
    }

    // Check for retest of broken low (SELL setup)
    if(waiting_for_retest_low && low_broken)
    {
        // Price should come back to test the first candle low level
        bool retest_condition = (current_high >= first_candle_low - retest_distance &&
                                current_close < first_candle_low &&
                                prev_close >= first_candle_low);

        if(retest_condition)
        {
            // Retest confirmed - place SELL order
            OpenSellTrade();
            waiting_for_retest_low = false;

            if(InpEnableLogging)
                Print("LOW retest confirmed - SELL signal at: ", DoubleToString(current_close, _Digits));
        }
    }
}

//+------------------------------------------------------------------+
//| Open Buy Trade                                                  |
//+------------------------------------------------------------------+
void OpenBuyTrade()
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    // Calculate position size
    double lot_size = CalculatePositionSize();
    if(lot_size <= 0)
        return;

    // Calculate stop loss and take profit
    double stop_loss = ask - (InpStopLossPips * pip_size);
    double take_profit = ask + (InpTakeProfitPips * pip_size);

    // Open BUY position
    if(trade.Buy(lot_size, _Symbol, ask, stop_loss, take_profit, InpTradeComment))
    {
        trades_today++;

        if(InpEnableLogging)
        {
            Print("BUY order opened:");
            Print("Entry: ", ask);
            Print("Stop Loss: ", stop_loss);
            Print("Take Profit: ", take_profit);
            Print("Lot Size: ", lot_size);
            Print("Risk: ", InpRiskPercent, "%");
        }
    }
    else
    {
        Print("Failed to open BUY order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Open Sell Trade                                                 |
//+------------------------------------------------------------------+
void OpenSellTrade()
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    // Calculate position size
    double lot_size = CalculatePositionSize();
    if(lot_size <= 0)
        return;

    // Calculate stop loss and take profit
    double stop_loss = bid + (InpStopLossPips * pip_size);
    double take_profit = bid - (InpTakeProfitPips * pip_size);

    // Open SELL position
    if(trade.Sell(lot_size, _Symbol, bid, stop_loss, take_profit, InpTradeComment))
    {
        trades_today++;

        if(InpEnableLogging)
        {
            Print("SELL order opened:");
            Print("Entry: ", bid);
            Print("Stop Loss: ", stop_loss);
            Print("Take Profit: ", take_profit);
            Print("Lot Size: ", lot_size);
            Print("Risk: ", InpRiskPercent, "%");
        }
    }
    else
    {
        Print("Failed to open SELL order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Calculate Position Size                                         |
//+------------------------------------------------------------------+
double CalculatePositionSize()
{
    double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = account_balance * InpRiskPercent / 100;

    // Calculate risk per pip
    double sl_distance = InpStopLossPips * pip_size;
    if(sl_distance <= 0)
        return 0;

    // Get contract size and tick value
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

    // Calculate position size
    double position_size = (risk_amount * tick_size) / (sl_distance * tick_value);

    // Normalize to lot step
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);

    position_size = MathFloor(position_size / lot_step) * lot_step;
    position_size = MathMax(position_size, min_lot);
    position_size = MathMin(position_size, max_lot);

    if(InpEnableLogging)
    {
        Print("Position Size Calculation:");
        Print("Account Balance: ", account_balance);
        Print("Risk Amount: ", risk_amount);
        Print("SL Distance: ", InpStopLossPips, " pips");
        Print("Position Size: ", position_size, " lots");
    }

    return position_size;
}

//+------------------------------------------------------------------+
//| Manage Positions                                                |
//+------------------------------------------------------------------+
void ManagePositions()
{
    if(!PositionSelect(_Symbol))
        return;

    double current_price = PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ?
                          SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                          SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    double entry_price = PositionGetDouble(POSITION_PRICE_OPEN);
    double current_sl = PositionGetDouble(POSITION_SL);
    double current_tp = PositionGetDouble(POSITION_TP);
    bool is_buy = PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY;

    double new_sl = current_sl;
    bool sl_modified = false;

    // Move to breakeven
    if(InpMoveToBreakeven)
    {
        double breakeven_distance = InpBreakevenPips * pip_size;

        if(is_buy)
        {
            // For BUY: move SL to breakeven when price is X pips in profit
            if(current_price >= entry_price + breakeven_distance && current_sl < entry_price)
            {
                new_sl = entry_price;
                sl_modified = true;

                if(InpEnableLogging)
                    Print("Moving stop loss to breakeven at: ", entry_price);
            }
        }
        else
        {
            // For SELL: move SL to breakeven when price is X pips in profit
            if(current_price <= entry_price - breakeven_distance && current_sl > entry_price)
            {
                new_sl = entry_price;
                sl_modified = true;

                if(InpEnableLogging)
                    Print("Moving stop loss to breakeven at: ", entry_price);
            }
        }
    }

    // Trailing stop
    if(InpUseTrailingStop && !sl_modified)
    {
        double trailing_distance = InpTrailingStopPips * pip_size;
        double trailing_step = InpTrailingStepPips * pip_size;

        if(is_buy)
        {
            // For BUY positions, trail stop loss upward
            double potential_sl = current_price - trailing_distance;

            // Only move SL up, and only if the move is significant
            if(potential_sl > current_sl + trailing_step)
            {
                new_sl = NormalizeDouble(potential_sl, _Digits);
                sl_modified = true;
            }
        }
        else
        {
            // For SELL positions, trail stop loss downward
            double potential_sl = current_price + trailing_distance;

            // Only move SL down, and only if the move is significant
            if(potential_sl < current_sl - trailing_step || current_sl == 0)
            {
                new_sl = NormalizeDouble(potential_sl, _Digits);
                sl_modified = true;
            }
        }
    }

    // Update stop loss if changed
    if(sl_modified && new_sl != current_sl)
    {
        if(trade.PositionModify(_Symbol, new_sl, current_tp))
        {
            if(InpEnableLogging)
                Print("Stop loss updated from ", current_sl, " to ", new_sl);
        }
        else
        {
            Print("Failed to modify stop loss. Error: ", GetLastError());
        }
    }
}

//+------------------------------------------------------------------+
//| OnTrade Event Handler                                           |
//+------------------------------------------------------------------+
void OnTrade()
{
    // Log trade events
    if(InpEnableLogging)
    {
        HistorySelect(TimeCurrent() - 3600, TimeCurrent()); // Last hour

        for(int i = HistoryDealsTotal() - 1; i >= 0; i--)
        {
            ulong deal_ticket = HistoryDealGetTicket(i);
            if(deal_ticket > 0)
            {
                string symbol = HistoryDealGetString(deal_ticket, DEAL_SYMBOL);
                long magic = HistoryDealGetInteger(deal_ticket, DEAL_MAGIC);

                if(symbol == _Symbol && magic == InpMagicNumber)
                {
                    ENUM_DEAL_TYPE deal_type = (ENUM_DEAL_TYPE)HistoryDealGetInteger(deal_ticket, DEAL_TYPE);
                    double deal_volume = HistoryDealGetDouble(deal_ticket, DEAL_VOLUME);
                    double deal_price = HistoryDealGetDouble(deal_ticket, DEAL_PRICE);
                    double deal_profit = HistoryDealGetDouble(deal_ticket, DEAL_PROFIT);

                    Print("TRADE EVENT - Type: ", EnumToString(deal_type),
                          " Volume: ", deal_volume,
                          " Price: ", deal_price,
                          " Profit: ", deal_profit);
                    break; // Only log the most recent deal
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Get Trading Status Summary                                       |
//+------------------------------------------------------------------+
string GetTradingStatus()
{
    string status = "=== FIRST CANDLE BREAKOUT STATUS ===\n";
    status += "Levels Set: " + (string)levels_set + "\n";

    if(levels_set)
    {
        status += "First Candle High: " + DoubleToString(first_candle_high, _Digits) + "\n";
        status += "First Candle Low: " + DoubleToString(first_candle_low, _Digits) + "\n";
        status += "High Broken: " + (string)high_broken + "\n";
        status += "Low Broken: " + (string)low_broken + "\n";
        status += "Waiting for High Retest: " + (string)waiting_for_retest_high + "\n";
        status += "Waiting for Low Retest: " + (string)waiting_for_retest_low + "\n";
    }

    status += "Trades Today: " + (string)trades_today + "/" + (string)InpMaxTradesPerDay + "\n";
    status += "Daily P&L: " + DoubleToString((AccountInfoDouble(ACCOUNT_BALANCE) - daily_start_balance), 2) + "\n";
    status += "================================";

    return status;
}

//+------------------------------------------------------------------+
//| Custom Comment Function                                          |
//+------------------------------------------------------------------+
void OnTimer()
{
    // Update chart comment every 30 seconds
    if(InpEnableLogging)
    {
        Comment(GetTradingStatus());
    }
}
